﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VisionIQ.Domain
{
    public class BaseEntity
    {
        public int Id { get; set; }

        [Column("reference_id")]
        public string ReferenceId { get; set; }
    }
    public interface IAuditableEntity
    {
        DateTime CreatedAt { get; set; }
        string? CreatedBy { get; set; }
        DateTime UpdatedAt { get; set; }
        string? UpdatedBy { get; set; }
        bool IsActive { get; set; }
    }

    public class AuditableEntity : BaseEntity, IAuditableEntity
    {
         // e.g., "12345"

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("created_by")]
        public string? CreatedBy { get; set; } // e.g., "UserId"

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [Column("updated_by")]
        public string UpdatedBy { get; set; } // e.g., "UserId"

        [Column("is_active")]
        public bool IsActive { get; set; } = true;
    }
}
