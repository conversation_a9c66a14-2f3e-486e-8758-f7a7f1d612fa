﻿using System.ComponentModel.DataAnnotations.Schema;

namespace VisionIQ.Domain
{
    public class Location :  AuditableEntity
    {

        [Column("location_name")]
        public string LocationName { get; set; }

        public string Address { get; set; } // e.g., "City", "Country", "Region"

        public string Latitude { get; set; } // e.g., "12345"

        public string Longitude { get; set; } // e.g., "67890"

        [Column("time_zone")]
        public string TimeZone { get; set; } // e.g., "12345"

        [Column("contact_info")]
        public string ContactInfo { get; set; } // e.g., "12345"
    }
}
