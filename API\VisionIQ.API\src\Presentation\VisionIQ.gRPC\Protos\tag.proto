﻿syntax = "proto3";

option csharp_namespace = "VisionIQ.Grpc.Protos";

service Tag {
  rpc CreateTag(CreateTagRequest) returns (CreateTagResponse);
  rpc UpdateTag(UpdateTagRequest) returns (UpdateTagResponse);
  rpc DeleteTag(DeleteTagRequest) returns (DeleteTagResponse);
  rpc GetTag(GetTagRequest) returns (GetTagResponse);
  rpc GetAllTags(GetAllTagsRequest) returns (GetAllTagsResponse); 
}

message GetTagRequest { 
  string TagId = 1;
}

message GetTagResponse {

 string TagId = 1;
 string TagName = 2; 
 string Description = 3;   
}

message CreateTagRequest {
  string TagName = 1;
  string Description = 2;   
}

message CreateTagResponse {
  string TagId = 1;
  string TagName = 2;
  string Description = 3;   
}

message UpdateTagRequest {
  string TagId = 1;
  string TagName = 2;
  string Description = 3;
}

message UpdateTagResponse {
  string TagId = 1;
  string TagName = 2;
  string Description = 3;   
}

message DeleteTagRequest {
  string TagId = 1; 
}

message DeleteTagResponse {
  bool Success = 1;
  string Message = 2;
}
 
message GetAllTagsRequest {
  // This message can be empty or can include filters if needed in the future
}
message GetAllTagsResponse {
  repeated TagInfo Tags = 1;  
}

message TagInfo {
  string TagId = 1;
  string TagName = 2;
  string Description = 3;  
}
