﻿using Microsoft.EntityFrameworkCore;
using VisionIQ.Domain;
using VisionIQ.Interfaces.Persistence;
using VisionIQ.Persistence.Contexts;

namespace VisionIQ.Persistence.Repositories
{
    public class BaseRepository<T> : IRepository<T> where T : BaseEntity
    {
        protected readonly ApplicationDbContext DbContext;
   
        public DbSet<T> Entities;
        public BaseRepository(ApplicationDbContext dbContext)
        {
            DbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
  
            Entities = DbContext.Set<T>();
        }
        public async Task<T> AddAsync(T entity)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                await Entities.AddAsync(entity);
                await DbContext.SaveChangesAsync();
                return entity;
            });
        }

        public async Task<T> UpdateAsync(T entity)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                DbContext.ChangeTracker.Clear();
                Entities.Attach(entity);
                DbContext.Entry(entity).State = EntityState.Modified;
                await DbContext.SaveChangesAsync();
                return entity;
            });
        }

        public async Task<T?> GetByReferenceIdAsync(string id)
        {
            return await Entities.AsNoTracking().FirstOrDefaultAsync(x => x.ReferenceId == id);
        }

        public async Task<IEnumerable<T>> GetAllAsync()
        {
            if (typeof(IAuditableEntity).IsAssignableFrom(typeof(T)))
            {
                return await Entities
                    .AsNoTracking()
                    .Where(x => ((IAuditableEntity)x).IsActive)
                    .ToListAsync();
            }

            return await Entities.AsNoTracking().ToListAsync();
        }

        public async Task DeleteAsync(string id)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                var entity = await GetByReferenceIdAsync(id);
                if (entity == null)
                    throw new ArgumentException($"Entity with ReferenceId '{id}' does not exist.", nameof(id));

                if (entity is IAuditableEntity auditable)
                {
                    auditable.IsActive = false;
                    await UpdateAsync(entity);
                }
                else
                {
                    Entities.Remove(entity);
                    await DbContext.SaveChangesAsync();
                }

                return Task.CompletedTask;
            });
        }

        public async Task<bool> ExistsAsync(string id)
        {
            if (typeof(IAuditableEntity).IsAssignableFrom(typeof(T)))
            {
                return await Entities.AsNoTracking()
                    .AnyAsync(x => x.ReferenceId == id && ((IAuditableEntity)x).IsActive);
            }

            return await Entities.AsNoTracking().AnyAsync(x => x.ReferenceId == id);
        }
        private async Task<TResult> ExecuteWithRetryAsync<TResult>(Func<Task<TResult>> operation)
        {
            const int maxRetries = 5;
            var retryDelay = 1000;
            var retries = 0;

            var strategy = DbContext.Database.CreateExecutionStrategy();

            return await strategy.ExecuteAsync(async () =>
            {
                while (true)
                {
                    await using var transaction = await DbContext.Database.BeginTransactionAsync();
                    try
                    {
                        var result = await operation();
                        await transaction.CommitAsync();
                        return result;
                    }
                    catch (DbUpdateException ex)
                    {
                        retries++;
                        await transaction.RollbackAsync();

                        if (retries >= maxRetries) throw;
                        await Task.Delay(retryDelay);
                        retryDelay *= 2;
                    }
                }
            });
        }
    }
}
