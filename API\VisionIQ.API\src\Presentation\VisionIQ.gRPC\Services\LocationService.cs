﻿using Grpc.Core;
using VisionIQ.CrossCuttingConcerns.Helper.VisionIQ.gRPC.Services.Guards;
using VisionIQ.Grpc.Mappers;
using VisionIQ.Grpc.Protos;
using VisionIQ.Interfaces.Repositories;
using static VisionIQ.Grpc.Protos.Location;

namespace VisionIQ.Grpc.Services;

public class LocationService  : LocationBase
{
    private readonly ILocationRepository _locationRepository;
    private readonly ILogger<LocationService> _logger;
    private readonly ILocationMapper _locationMapper;

    public LocationService(ILocationRepository locationRepository, ILogger<LocationService> logger, ILocationMapper locationMapper)
    {
        _locationRepository = locationRepository;
        _logger = logger;
        _locationMapper = locationMapper;
    }

    public override async Task<GetLocationResponse> GetLocation(GetLocationRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Getting location with ID: {LocationId}", request.LocationId);

            Guard.Against.NullOrEmpty(request.LocationId, nameof(request.LocationId));

            var location = await _locationRepository.GetByReferenceIdAsync(request.LocationId);

            if (location == null)
            {
                _logger.LogWarning("Location not found with ID: {LocationId}", request.LocationId);
                throw new RpcException(new Status(StatusCode.NotFound, "Location not found"));
            }

            _logger.LogInformation("Location retrieved successfully: {LocationId}", location.ReferenceId);

            return _locationMapper.MapToGetResponse(location);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid request data: {Message}", ex.Message);
            throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
        }
        catch (RpcException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving location with ID: {LocationId}", request.LocationId);
            throw new RpcException(new Status(StatusCode.Internal, "An error occurred while retrieving the location"));
        }

    }

    public override async Task<CreateLocationResponse> CreateLocation(CreateLocationRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Creating location: {LocationName}", request.LocationName);

            Guard.Against.NullOrEmpty(request.LocationName, nameof(request.LocationName));

            var location = _locationMapper.MapToEntity(request);
            var createdLocation = await _locationRepository.AddAsync(location);

            _logger.LogInformation("Location created successfully with ID: {LocationId}", createdLocation.ReferenceId);

            return _locationMapper.MapToCreateResponse(createdLocation);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid request data: {Message}", ex.Message);
            throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating location: {LocationName}", request.LocationName);
            throw new RpcException(new Status(StatusCode.Internal, "An error occurred while creating the location"));
        }
    }

    public override async Task<UpdateLocationResponse> UpdateLocation(UpdateLocationRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Updating location with ID: {LocationId}", request.LocationId);

            Guard.Against.NullOrEmpty(request.LocationId, nameof(request.LocationId));

            var existingLocation = await _locationRepository.GetByReferenceIdAsync(request.LocationId);

            if (existingLocation == null)
            {
                _logger.LogWarning("Location not found with ID: {LocationId}", request.LocationId);
                throw new RpcException(new Status(StatusCode.NotFound, "Location not found"));
            }

            _locationMapper.MapToEntity(request, existingLocation);
            var updatedLocation = await _locationRepository.UpdateAsync(existingLocation);

            _logger.LogInformation("Location updated successfully: {LocationId}", updatedLocation.ReferenceId);

            return _locationMapper.MapToUpdateResponse(updatedLocation);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid request data: {Message}", ex.Message);
            throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
        }
        catch (RpcException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location with ID: {LocationId}", request.LocationId);
            throw new RpcException(new Status(StatusCode.Internal, "An error occurred while updating the location"));
        }
    }

    public override async Task<DeleteLocationResponse> DeleteLocation(DeleteLocationRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Deleting location with ID: {LocationId}", request.LocationId);

            Guard.Against.NullOrEmpty(request.LocationId, nameof(request.LocationId));

            await _locationRepository.DeleteAsync(request.LocationId);

            _logger.LogInformation("Location deleted successfully: {LocationId}", request.LocationId);

            return new DeleteLocationResponse
            {
                Success = true,
                Message = "Location deleted successfully"
            };
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid request data: {Message}", ex.Message);
            throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
        }
        catch (RpcException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting location with ID: {LocationId}", request.LocationId);
            throw new RpcException(new Status(StatusCode.Internal, "An error occurred while deleting the location"));
        }
    }

    public override async Task<GetAllLocationsResponse> GetAllLocations(GetAllLocationsRequest request, ServerCallContext context)
    {

        try
        {
            _logger.LogInformation("Getting all locations");
            var locations = await _locationRepository.GetAllAsync();
            var enumerable = locations.ToList();
            if (!enumerable.Any())
            {
                _logger.LogInformation("No locations found");
                return new GetAllLocationsResponse();
            }
            var locationInfos = enumerable.Select(location => _locationMapper.MapToLocationInfo(location)).ToList();
            _logger.LogInformation("Retrieved {Count} locations", locationInfos.Count);
            return new GetAllLocationsResponse
            {
                Locations = { locationInfos }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all locations");
            throw new RpcException(new Status(StatusCode.Internal, "An error occurred while retrieving locations"));
        }
    }

    public override async Task<GetPagedLocationsResponse> GetPagedLocationsAsync(GetPagedLocationsRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Getting paged locations - Page: {PageNumber}, Size: {PageSize}", request.PageNumber, request.PageSize);

            var pageNumber = request.PageNumber > 0 ? request.PageNumber : 1;
            var pageSize = request.PageSize > 0 ? request.PageSize : 10;

            var locations = await _locationRepository.GetAllAsync();
            var enumerable = locations.ToList();
            var totalCount = enumerable.Count();

            var pagedLocations = enumerable
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(location => _locationMapper.MapToLocationInfo(location))
                .ToList();

            _logger.LogInformation("Retrieved {Count} locations out of {Total}", pagedLocations.Count, totalCount);

            return new GetPagedLocationsResponse
            {
                Locations = { pagedLocations },
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paged locations");
            throw new RpcException(new Status(StatusCode.Internal, "An error occurred while retrieving locations"));
        }
    }

}