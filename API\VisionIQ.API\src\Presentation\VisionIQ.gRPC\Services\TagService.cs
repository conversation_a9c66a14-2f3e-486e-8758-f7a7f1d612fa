﻿
using Grpc.Core;
using VisionIQ.CrossCuttingConcerns.Helper.VisionIQ.gRPC.Services.Guards;
using VisionIQ.Grpc.Mappers;
using VisionIQ.Grpc.Protos;
using VisionIQ.Interfaces.Repositories;
using static VisionIQ.Grpc.Protos.Tag;
namespace VisionIQ.Grpc.Services
{
    public class TagService : TagBase
    {
        private readonly ILogger<TagService> _logger;
        private readonly ITagRepository _tagRepository;
        private readonly ITagMapper _tagMapper;

        public TagService(ILogger<TagService> logger, ITagRepository tagRepository, ITagMapper tagMapper)
        {
            _logger = logger;
            _tagRepository = tagRepository;
            _tagMapper = tagMapper;
        }
        public override async Task<CreateTagResponse> CreateTag(CreateTagRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation("Creating tag: {TagName}", request.TagName);

                Guard.Against.NullOrEmpty(request.TagName, nameof(request.TagName));

                var tag = _tagMapper.MapToEntity(request);
                var createdTag = await _tagRepository.AddAsync(tag);

                _logger.LogInformation("Tag created successfully with ID: {TagId}", createdTag.ReferenceId);

                return _tagMapper.MapToCreateResponse(createdTag);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid request data: {Message}", ex.Message);
                throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tag: {TagName}", request.TagName);
                throw new RpcException(new Status(StatusCode.Internal, "An error occurred while creating the tag"));
            }
        }

        public override async Task<UpdateTagResponse> UpdateTag(UpdateTagRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation("Updating tag with ID: {TagId}", request.TagId);

                Guard.Against.NullOrEmpty(request.TagId, nameof(request.TagId));
                Guard.Against.NullOrEmpty(request.TagName, nameof(request.TagName));

                var existingTag = await _tagRepository.GetByReferenceIdAsync(request.TagId);

                if (existingTag == null)
                {
                    _logger.LogWarning("Tag not found with ID: {TagId}", request.TagId);
                    throw new RpcException(new Status(StatusCode.NotFound, "Tag not found"));
                }

                _tagMapper.MapToEntity(request, existingTag);
                var updatedTag = await _tagRepository.UpdateAsync(existingTag);

                _logger.LogInformation("Tag updated successfully: {TagId}", updatedTag.ReferenceId);

                return _tagMapper.MapToUpdateResponse(updatedTag);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid request data: {Message}", ex.Message);
                throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tag with ID: {TagId}", request.TagId);
                throw new RpcException(new Status(StatusCode.Internal, "An error occurred while updating the tag"));
            }
        }

        public override async Task<DeleteTagResponse> DeleteTag(DeleteTagRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation("Deleting tag with ID: {TagId}", request.TagId);

                Guard.Against.NullOrEmpty(request.TagId, nameof(request.TagId));

                await _tagRepository.DeleteAsync(request.TagId);

                _logger.LogInformation("Tag deleted successfully: {TagId}", request.TagId);

                return new DeleteTagResponse
                {
                    Success = true,
                    Message = "Tag deleted successfully"
                };
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid request data: {Message}", ex.Message);
                throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tag with ID: {TagId}", request.TagId);
                throw new RpcException(new Status(StatusCode.Internal, "An error occurred while deleting the tag"));
            }
        }

        public override async Task<GetAllTagsResponse> GetAllTags(GetAllTagsRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation("Getting all tags");

                var tags = await _tagRepository.GetAllAsync();
                var tagsList = tags.ToList();

                if (tagsList.Count == 0)
                {
                    _logger.LogInformation("No tags found");
                    return new GetAllTagsResponse();
                }

                var tagInfos = tagsList.Select(_tagMapper.MapToTagInfo).ToList();

                _logger.LogInformation("Retrieved {Count} tags", tagInfos.Count);

                return new GetAllTagsResponse
                {
                    Tags = { tagInfos }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all tags");
                throw new RpcException(new Status(StatusCode.Internal, "An error occurred while retrieving tags"));
            }
        }

        public override async Task<GetTagResponse> GetTag(GetTagRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation("Getting tag with ID: {TagId}", request.TagId);

                Guard.Against.NullOrEmpty(request.TagId, nameof(request.TagId));

                var tag = await _tagRepository.GetByReferenceIdAsync(request.TagId);

                if (tag == null)
                {
                    _logger.LogWarning("Tag not found with ID: {TagId}", request.TagId);
                    throw new RpcException(new Status(StatusCode.NotFound, "Tag not found"));
                }

                _logger.LogInformation("Tag retrieved successfully: {TagId}", tag.ReferenceId);

                return _tagMapper.MapToGetResponse(tag);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid request data: {Message}", ex.Message);
                throw new RpcException(new Status(StatusCode.InvalidArgument, ex.Message));
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag with ID: {TagId}", request.TagId);
                throw new RpcException(new Status(StatusCode.Internal, "An error occurred while retrieving the tag"));
            }
        }
    }
}
