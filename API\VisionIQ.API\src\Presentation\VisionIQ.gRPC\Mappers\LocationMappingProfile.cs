﻿using AutoMapper;
using VisionIQ.Grpc.Protos;
using Location = VisionIQ.Domain.Location; // Adjust the namespace according to your project structure

namespace VisionIQ.Grpc.Mappers
{
    public class LocationMappingProfile : Profile
    {
        public LocationMappingProfile()
        {
            // Request to Entity mappings
            CreateMap<CreateLocationRequest, Location>()
                .ForMember(dest => dest.ReferenceId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());

            CreateMap<UpdateLocationRequest, Location>()
                .ForMember(dest => dest.ReferenceId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());

            // Entity to Response mappings
            CreateMap<Location, CreateLocationResponse>()
                .ForMember(dest => dest.LocationId, opt => opt.MapFrom(src => src.ReferenceId));

            CreateMap<Location, GetLocationResponse>()
                .ForMember(dest => dest.LocationId, opt => opt.MapFrom(src => src.ReferenceId));

            CreateMap<Location, UpdateLocationResponse>()
                .ForMember(dest => dest.LocationId, opt => opt.MapFrom(src => src.ReferenceId));

            CreateMap<Location, LocationInfo>()
                .ForMember(dest => dest.LocationId, opt => opt.MapFrom(src => src.ReferenceId));
        }
    }
}
