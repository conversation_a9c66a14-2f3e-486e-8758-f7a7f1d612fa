﻿using Microsoft.EntityFrameworkCore;
using VisionIQ.CrossCuttingConcerns.Extensions;
using VisionIQ.Domain;
using VisionIQ.Interfaces.Persistence;

namespace VisionIQ.Persistence.Contexts
{
    public class ApplicationDbContext :  DbContext, IApplicationDbContext
    {
        private readonly ILoggedInUserService _loggedInUserService;

        protected ApplicationDbContext(DbContextOptions options, ILoggedInUserService loggedInUserService) : base(options)
        {
            _loggedInUserService = loggedInUserService;
        }

        public DbSet<Location> Locations { get; set; }
        public DbSet<Tag> Tags { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
        }
        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new())
        {
            foreach (var entry in ChangeTracker.Entries<AuditableEntity>())
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.ReferenceId = entry.Entity.ReferenceId.IsNotNullOrWhiteSpace() ? entry.Entity.ReferenceId : Guid.NewGuid().ToString();
                        entry.Entity.CreatedAt = DateTime.Now;
                        entry.Entity.CreatedBy = entry.Entity.CreatedBy.IsNullOrEmpty() ? _loggedInUserService.UserId : entry.Entity.CreatedBy;
                        entry.Entity.UpdatedAt = DateTime.Now;
                        entry.Entity.UpdatedBy = entry.Entity.CreatedBy.IsNullOrEmpty() ? _loggedInUserService.UserId : entry.Entity.CreatedBy;
                        entry.Entity.IsActive = true;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = DateTime.Now;
                        entry.Entity.UpdatedBy = _loggedInUserService.UserId.IsNullOrEmpty() ? entry.Entity.UpdatedBy : _loggedInUserService.UserId;
                        break;
                    case EntityState.Detached:
                        break;
                    case EntityState.Unchanged:
                        break;
                    case EntityState.Deleted:
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }

            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
