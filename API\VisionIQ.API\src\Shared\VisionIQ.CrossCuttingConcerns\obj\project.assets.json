{"version": 3, "targets": {"net9.0": {"VisionIQ.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/VisionIQ.Domain.dll": {}}, "runtime": {"bin/placeholder/VisionIQ.Domain.dll": {}}}}}, "libraries": {"VisionIQ.Domain/1.0.0": {"type": "project", "path": "../../Domain/VisionIQ.Domain/VisionIQ.Domain.csproj", "msbuildProject": "../../Domain/VisionIQ.Domain/VisionIQ.Domain.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["VisionIQ.Domain >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj", "projectName": "VisionIQ.CrossCuttingConcerns", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}