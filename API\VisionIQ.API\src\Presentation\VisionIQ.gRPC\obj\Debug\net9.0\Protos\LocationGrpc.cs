// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/location.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace VisionIQ.Grpc.Protos {
  public static partial class Location
  {
    static readonly string __ServiceName = "Location";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.CreateLocationRequest> __Marshaller_CreateLocationRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.CreateLocationRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.CreateLocationResponse> __Marshaller_CreateLocationResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.CreateLocationResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.UpdateLocationRequest> __Marshaller_UpdateLocationRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.UpdateLocationRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.UpdateLocationResponse> __Marshaller_UpdateLocationResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.UpdateLocationResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.DeleteLocationRequest> __Marshaller_DeleteLocationRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.DeleteLocationRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.DeleteLocationResponse> __Marshaller_DeleteLocationResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.DeleteLocationResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.GetLocationRequest> __Marshaller_GetLocationRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.GetLocationRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.GetLocationResponse> __Marshaller_GetLocationResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.GetLocationResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.GetAllLocationsRequest> __Marshaller_GetAllLocationsRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.GetAllLocationsRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.GetAllLocationsResponse> __Marshaller_GetAllLocationsResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.GetAllLocationsResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest> __Marshaller_GetPagedLocationsRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse> __Marshaller_GetPagedLocationsResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VisionIQ.Grpc.Protos.CreateLocationRequest, global::VisionIQ.Grpc.Protos.CreateLocationResponse> __Method_CreateLocation = new grpc::Method<global::VisionIQ.Grpc.Protos.CreateLocationRequest, global::VisionIQ.Grpc.Protos.CreateLocationResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CreateLocation",
        __Marshaller_CreateLocationRequest,
        __Marshaller_CreateLocationResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VisionIQ.Grpc.Protos.UpdateLocationRequest, global::VisionIQ.Grpc.Protos.UpdateLocationResponse> __Method_UpdateLocation = new grpc::Method<global::VisionIQ.Grpc.Protos.UpdateLocationRequest, global::VisionIQ.Grpc.Protos.UpdateLocationResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "UpdateLocation",
        __Marshaller_UpdateLocationRequest,
        __Marshaller_UpdateLocationResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VisionIQ.Grpc.Protos.DeleteLocationRequest, global::VisionIQ.Grpc.Protos.DeleteLocationResponse> __Method_DeleteLocation = new grpc::Method<global::VisionIQ.Grpc.Protos.DeleteLocationRequest, global::VisionIQ.Grpc.Protos.DeleteLocationResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "DeleteLocation",
        __Marshaller_DeleteLocationRequest,
        __Marshaller_DeleteLocationResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VisionIQ.Grpc.Protos.GetLocationRequest, global::VisionIQ.Grpc.Protos.GetLocationResponse> __Method_GetLocation = new grpc::Method<global::VisionIQ.Grpc.Protos.GetLocationRequest, global::VisionIQ.Grpc.Protos.GetLocationResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetLocation",
        __Marshaller_GetLocationRequest,
        __Marshaller_GetLocationResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VisionIQ.Grpc.Protos.GetAllLocationsRequest, global::VisionIQ.Grpc.Protos.GetAllLocationsResponse> __Method_GetAllLocations = new grpc::Method<global::VisionIQ.Grpc.Protos.GetAllLocationsRequest, global::VisionIQ.Grpc.Protos.GetAllLocationsResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetAllLocations",
        __Marshaller_GetAllLocationsRequest,
        __Marshaller_GetAllLocationsResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest, global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse> __Method_GetPagedLocationsAsync = new grpc::Method<global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest, global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetPagedLocationsAsync",
        __Marshaller_GetPagedLocationsRequest,
        __Marshaller_GetPagedLocationsResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of Location</summary>
    [grpc::BindServiceMethod(typeof(Location), "BindService")]
    public abstract partial class LocationBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VisionIQ.Grpc.Protos.CreateLocationResponse> CreateLocation(global::VisionIQ.Grpc.Protos.CreateLocationRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VisionIQ.Grpc.Protos.UpdateLocationResponse> UpdateLocation(global::VisionIQ.Grpc.Protos.UpdateLocationRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VisionIQ.Grpc.Protos.DeleteLocationResponse> DeleteLocation(global::VisionIQ.Grpc.Protos.DeleteLocationRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VisionIQ.Grpc.Protos.GetLocationResponse> GetLocation(global::VisionIQ.Grpc.Protos.GetLocationRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VisionIQ.Grpc.Protos.GetAllLocationsResponse> GetAllLocations(global::VisionIQ.Grpc.Protos.GetAllLocationsRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse> GetPagedLocationsAsync(global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(LocationBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_CreateLocation, serviceImpl.CreateLocation)
          .AddMethod(__Method_UpdateLocation, serviceImpl.UpdateLocation)
          .AddMethod(__Method_DeleteLocation, serviceImpl.DeleteLocation)
          .AddMethod(__Method_GetLocation, serviceImpl.GetLocation)
          .AddMethod(__Method_GetAllLocations, serviceImpl.GetAllLocations)
          .AddMethod(__Method_GetPagedLocationsAsync, serviceImpl.GetPagedLocationsAsync).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, LocationBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_CreateLocation, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VisionIQ.Grpc.Protos.CreateLocationRequest, global::VisionIQ.Grpc.Protos.CreateLocationResponse>(serviceImpl.CreateLocation));
      serviceBinder.AddMethod(__Method_UpdateLocation, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VisionIQ.Grpc.Protos.UpdateLocationRequest, global::VisionIQ.Grpc.Protos.UpdateLocationResponse>(serviceImpl.UpdateLocation));
      serviceBinder.AddMethod(__Method_DeleteLocation, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VisionIQ.Grpc.Protos.DeleteLocationRequest, global::VisionIQ.Grpc.Protos.DeleteLocationResponse>(serviceImpl.DeleteLocation));
      serviceBinder.AddMethod(__Method_GetLocation, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VisionIQ.Grpc.Protos.GetLocationRequest, global::VisionIQ.Grpc.Protos.GetLocationResponse>(serviceImpl.GetLocation));
      serviceBinder.AddMethod(__Method_GetAllLocations, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VisionIQ.Grpc.Protos.GetAllLocationsRequest, global::VisionIQ.Grpc.Protos.GetAllLocationsResponse>(serviceImpl.GetAllLocations));
      serviceBinder.AddMethod(__Method_GetPagedLocationsAsync, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest, global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse>(serviceImpl.GetPagedLocationsAsync));
    }

  }
}
#endregion
