﻿using System.Globalization;
using System.Text.RegularExpressions;

namespace VisionIQ.CrossCuttingConcerns.Extensions;

public static partial class Extensions
{
    public static bool IsValidGuid(this string value)
    {
        return Guid.TryParse(value, out _);
    }
    public static bool IsNullOrEmpty(this string? @this)
    {
        return string.IsNullOrEmpty(@this);
    }
    public static bool IsNotNullOrEmpty(this string @this)
    {
        return !string.IsNullOrEmpty(@this);
    }
    public static bool IsNotNullOrWhiteSpace(this string value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }
    public static bool IsNullOrWhiteSpace(this string value)
    {
        return string.IsNullOrWhiteSpace(value);
    }
    public static bool IsNumeric(this string value)
    {
        return !Regex.IsMatch(value, "[^0-9]");
    }
    public static string ToTitleCase(this string value)
    {
        return new CultureInfo("en-US").TextInfo.ToTitleCase(value);
    }
    public static string ToTitleCase(this string value, CultureInfo cultureInfo)
    {
        return cultureInfo.TextInfo.ToTitleCase(value);
    }
    public static T ToEnum<T>(this string value)
    {
        Type enumType = typeof(T);
        return (T)Enum.Parse(enumType, value);
    }

    public static int ParseToInt(this string input)
    {
        if (string.IsNullOrWhiteSpace(input)) return -1;
        var isSuccess = int.TryParse(input, out var output);

        if (isSuccess) return output;
        return -1;
    }

    public static bool IsNull(this string value)
    {
        return value == null;
    }
}