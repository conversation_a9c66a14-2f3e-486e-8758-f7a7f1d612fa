﻿using AutoMapper;
using VisionIQ.Grpc.Protos;
using Tag = VisionIQ.Domain.Tag;

namespace VisionIQ.Grpc.Mappers
{
    public class TagMapper : ITagMapper
    {
        private readonly IMapper _mapper;

        public TagMapper(IMapper mapper)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public Tag MapToEntity(CreateTagRequest request)
        {
            return _mapper.Map<Tag>(request);
        }

        public void MapToEntity(UpdateTagRequest request, Tag entity)
        {
            _mapper.Map(request, entity);
        }

        public CreateTagResponse MapToCreateResponse(Tag entity)
        {
            return _mapper.Map<CreateTagResponse>(entity);
        }

        public GetTagResponse MapToGetResponse(Tag entity)
        {
            return _mapper.Map<GetTagResponse>(entity);
        }

        public UpdateTagResponse MapToUpdateResponse(Tag entity)
        {
            return _mapper.Map<UpdateTagResponse>(entity);
        }

        public TagInfo MapToTagInfo(Tag entity)
        {
            return _mapper.Map<TagInfo>(entity);
        }
    }

    public interface ITagMapper
    {
        Tag MapToEntity(CreateTagRequest request);
        void MapToEntity(UpdateTagRequest request, Tag entity);
        CreateTagResponse MapToCreateResponse(Tag entity);
        GetTagResponse MapToGetResponse(Tag entity);
        UpdateTagResponse MapToUpdateResponse(Tag entity);
        TagInfo MapToTagInfo(Tag entity);
    }
}
