﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36310.24
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.Grpc", "src\Presentation\VisionIQ.gRPC\VisionIQ.Grpc.csproj", "{DDFE9CC7-59B4-4A25-8D1C-6CF286E57496}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Domain", "Domain", "{6283137D-A06F-487C-9E9E-5BD7FDB4D1A4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{160F5D69-F257-4D86-B195-4DD3F2D317A9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Application", "Application", "{FBE49532-8739-43B4-8A16-A7FFFF36AA5F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.Domain", "src\Domain\VisionIQ.Domain\VisionIQ.Domain.csproj", "{8C00B327-EFB0-42FF-99DB-E2E60386D052}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.Interfaces", "src\Application\VisionIQ.Application.Interfaces\VisionIQ.Interfaces.csproj", "{2107F595-834A-4710-9A68-71A0EC7BB901}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.UseCases", "src\Application\VisionIQ.Application.UseCases\VisionIQ.UseCases.csproj", "{0458E98A-880D-41CB-BF78-26B45922FCBE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.Infrastructure", "src\Infrastructure\VisionIQ.Infrastructure\VisionIQ.Infrastructure.csproj", "{860C301D-33BF-42AE-8B74-6CB22A96B796}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.Persistence", "src\Infrastructure\VisionIQ.Persistence\VisionIQ.Persistence.csproj", "{17CD9A63-830E-4073-9699-512CDF138E92}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.API", "src\Presentation\VisionIQ.API\VisionIQ.API.csproj", "{D03B8C21-6294-45DD-91E6-90C6E3688929}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{8A085C59-516E-41C2-962C-9F23734D4FF4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.CrossCuttingConcerns", "src\Shared\VisionIQ.CrossCuttingConcerns\VisionIQ.CrossCuttingConcerns.csproj", "{6E508BF9-7423-48E6-B300-F40B5B82C81B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisionIQ.Shared.Lib", "src\Shared\VisionIQ.Shared.Lib\VisionIQ.Shared.Lib.csproj", "{0B6556C9-92C9-4F33-AABF-AB95C923D72A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DDFE9CC7-59B4-4A25-8D1C-6CF286E57496}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDFE9CC7-59B4-4A25-8D1C-6CF286E57496}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDFE9CC7-59B4-4A25-8D1C-6CF286E57496}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDFE9CC7-59B4-4A25-8D1C-6CF286E57496}.Release|Any CPU.Build.0 = Release|Any CPU
		{8C00B327-EFB0-42FF-99DB-E2E60386D052}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8C00B327-EFB0-42FF-99DB-E2E60386D052}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8C00B327-EFB0-42FF-99DB-E2E60386D052}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8C00B327-EFB0-42FF-99DB-E2E60386D052}.Release|Any CPU.Build.0 = Release|Any CPU
		{2107F595-834A-4710-9A68-71A0EC7BB901}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2107F595-834A-4710-9A68-71A0EC7BB901}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2107F595-834A-4710-9A68-71A0EC7BB901}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2107F595-834A-4710-9A68-71A0EC7BB901}.Release|Any CPU.Build.0 = Release|Any CPU
		{0458E98A-880D-41CB-BF78-26B45922FCBE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0458E98A-880D-41CB-BF78-26B45922FCBE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0458E98A-880D-41CB-BF78-26B45922FCBE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0458E98A-880D-41CB-BF78-26B45922FCBE}.Release|Any CPU.Build.0 = Release|Any CPU
		{860C301D-33BF-42AE-8B74-6CB22A96B796}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{860C301D-33BF-42AE-8B74-6CB22A96B796}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{860C301D-33BF-42AE-8B74-6CB22A96B796}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{860C301D-33BF-42AE-8B74-6CB22A96B796}.Release|Any CPU.Build.0 = Release|Any CPU
		{17CD9A63-830E-4073-9699-512CDF138E92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{17CD9A63-830E-4073-9699-512CDF138E92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{17CD9A63-830E-4073-9699-512CDF138E92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{17CD9A63-830E-4073-9699-512CDF138E92}.Release|Any CPU.Build.0 = Release|Any CPU
		{D03B8C21-6294-45DD-91E6-90C6E3688929}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D03B8C21-6294-45DD-91E6-90C6E3688929}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D03B8C21-6294-45DD-91E6-90C6E3688929}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D03B8C21-6294-45DD-91E6-90C6E3688929}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E508BF9-7423-48E6-B300-F40B5B82C81B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E508BF9-7423-48E6-B300-F40B5B82C81B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E508BF9-7423-48E6-B300-F40B5B82C81B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E508BF9-7423-48E6-B300-F40B5B82C81B}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B6556C9-92C9-4F33-AABF-AB95C923D72A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B6556C9-92C9-4F33-AABF-AB95C923D72A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B6556C9-92C9-4F33-AABF-AB95C923D72A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B6556C9-92C9-4F33-AABF-AB95C923D72A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DDFE9CC7-59B4-4A25-8D1C-6CF286E57496} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8C00B327-EFB0-42FF-99DB-E2E60386D052} = {6283137D-A06F-487C-9E9E-5BD7FDB4D1A4}
		{2107F595-834A-4710-9A68-71A0EC7BB901} = {FBE49532-8739-43B4-8A16-A7FFFF36AA5F}
		{0458E98A-880D-41CB-BF78-26B45922FCBE} = {FBE49532-8739-43B4-8A16-A7FFFF36AA5F}
		{860C301D-33BF-42AE-8B74-6CB22A96B796} = {160F5D69-F257-4D86-B195-4DD3F2D317A9}
		{17CD9A63-830E-4073-9699-512CDF138E92} = {160F5D69-F257-4D86-B195-4DD3F2D317A9}
		{D03B8C21-6294-45DD-91E6-90C6E3688929} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6E508BF9-7423-48E6-B300-F40B5B82C81B} = {8A085C59-516E-41C2-962C-9F23734D4FF4}
		{0B6556C9-92C9-4F33-AABF-AB95C923D72A} = {8A085C59-516E-41C2-962C-9F23734D4FF4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {28A15C89-8951-411A-994E-786E3EDF86E8}
	EndGlobalSection
EndGlobal
