﻿using AutoMapper;
using VisionIQ.Grpc.Protos;
using Tag = VisionIQ.Domain.Tag;

namespace VisionIQ.Grpc.Mappers
{
    public class TagMappingProfile : Profile
    {
        public TagMappingProfile()
        {
            // Request to Entity mappings
            CreateMap<CreateTagRequest, Tag>()
                .ForMember(dest => dest.ReferenceId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore());


            CreateMap<UpdateTagRequest, Tag>()
                .ForMember(dest => dest.ReferenceId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore());
                

            // Entity to Response mappings
            CreateMap<Tag, CreateTagResponse>()
                .ForMember(dest => dest.TagId, opt => opt.MapFrom(src => src.ReferenceId));

            CreateMap<Tag, GetTagResponse>()
                .ForMember(dest => dest.TagId, opt => opt.MapFrom(src => src.ReferenceId));

            CreateMap<Tag, UpdateTagResponse>()
                .ForMember(dest => dest.TagId, opt => opt.MapFrom(src => src.ReferenceId));

            CreateMap<Tag, TagInfo>()
                .ForMember(dest => dest.TagId, opt => opt.MapFrom(src => src.ReferenceId));
        }
    }
}
