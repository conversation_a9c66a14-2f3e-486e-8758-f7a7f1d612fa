{"version": 3, "targets": {"net9.0": {"VisionIQ.CrossCuttingConcerns/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"VisionIQ.Domain": "1.0.0"}, "compile": {"bin/placeholder/VisionIQ.CrossCuttingConcerns.dll": {}}, "runtime": {"bin/placeholder/VisionIQ.CrossCuttingConcerns.dll": {}}}, "VisionIQ.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/VisionIQ.Domain.dll": {}}, "runtime": {"bin/placeholder/VisionIQ.Domain.dll": {}}}, "VisionIQ.Interfaces/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"VisionIQ.Domain": "1.0.0"}, "compile": {"bin/placeholder/VisionIQ.Interfaces.dll": {}}, "runtime": {"bin/placeholder/VisionIQ.Interfaces.dll": {}}}}}, "libraries": {"VisionIQ.CrossCuttingConcerns/1.0.0": {"type": "project", "path": "../../Shared/VisionIQ.CrossCuttingConcerns/VisionIQ.CrossCuttingConcerns.csproj", "msbuildProject": "../../Shared/VisionIQ.CrossCuttingConcerns/VisionIQ.CrossCuttingConcerns.csproj"}, "VisionIQ.Domain/1.0.0": {"type": "project", "path": "../../Domain/VisionIQ.Domain/VisionIQ.Domain.csproj", "msbuildProject": "../../Domain/VisionIQ.Domain/VisionIQ.Domain.csproj"}, "VisionIQ.Interfaces/1.0.0": {"type": "project", "path": "../../Application/VisionIQ.Application.Interfaces/VisionIQ.Interfaces.csproj", "msbuildProject": "../../Application/VisionIQ.Application.Interfaces/VisionIQ.Interfaces.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["VisionIQ.CrossCuttingConcerns >= 1.0.0", "VisionIQ.Interfaces >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj", "projectName": "VisionIQ.Persistence", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj"}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}