{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DBProvider": "mysql", "Default": "Server=10.11.20.44;Port=3306;Database=visoniq;Uid=root;Pwd=root;Pooling=true;Min Pool Size=0;Max Pool Size=5000;Connection Timeout=60;"}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Enrichers.ClientInfo"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "System": "Error", "Quartz": "Error"}}, "WriteTo": [{"Name": "File", "Args": {"path": "C:\\CP\\CP_Web_log-.txt", "fileSizeLimitBytes": "524288000", "rollOnFileSizeLimit": true, "retainedFileCountLimit": null, "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithCorrelationId", "WithClientIp"]}, "AllowedHosts": "*", "Kestrel": {"EndpointDefaults": {"Protocols": "Http2"}}}