﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VisionIQ.Interfaces.Persistence
{
    public interface ILoggedInUserService
    {
        string? UserId { get; }
        string Role { get; }
        string IpAddress { get; }
        string RequestedUrl { get; }
        List<string> Permissions { get; }
        string LoginName { get; }
        bool IsAuthenticated { get; }
        bool IsAdministrator { get; }
        bool IsSiteAdmin { get; }
    }
}
