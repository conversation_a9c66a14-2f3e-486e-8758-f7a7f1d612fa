﻿syntax = "proto3";

option csharp_namespace = "VisionIQ.Grpc.Protos";

service Location {
  rpc CreateLocation(CreateLocationRequest) returns (CreateLocationResponse);
  rpc UpdateLocation(UpdateLocationRequest) returns (UpdateLocationResponse);
  rpc DeleteLocation(DeleteLocationRequest) returns (DeleteLocationResponse);
  rpc GetLocation(GetLocationRequest) returns (GetLocationResponse);
  rpc GetAllLocations(GetAllLocationsRequest) returns (GetAllLocationsResponse);
  rpc GetPagedLocationsAsync(GetPagedLocationsRequest) returns (GetPagedLocationsResponse);
}

message GetLocationRequest { 
  string LocationId = 1;
}

message GetLocationResponse {

 string LocationId = 1;
 string LocationName = 2; 
 string Latitude = 3; 
 string Longitude = 4; 
 string TimeZone = 5; 
}

message CreateLocationRequest {
  string LocationName = 1;
  string Address = 2;
  string Latitude = 3;
  string Longitude = 4;
  string TimeZone = 5;
  string ContactInfo = 6;
  string CreatedBy = 7;
}

message CreateLocationResponse {
  string LocationId = 1;
  string LocationName = 2;
  string Address = 3;
  string Latitude = 4;
  string Longitude = 5;
  string TimeZone = 6;
  string ContactInfo = 7;
  string CreatedBy = 8;
}

message UpdateLocationRequest {
  string LocationId = 1;
  string LocationName = 2;
  string Address = 3;
  string Latitude = 4;
  string Longitude = 5;
  string TimeZone = 6;
  string ContactInfo = 7;
  string UpdatedBy = 8;
}

message UpdateLocationResponse {
  string LocationId = 1;
  string LocationName = 2;
  string Address = 3;
  string Latitude = 4;
  string Longitude = 5;
  string TimeZone = 6;
  string ContactInfo = 7;
  string UpdatedBy = 8;
}

message DeleteLocationRequest {
  string LocationId = 1;
  string DeletedBy = 2;
}

message DeleteLocationResponse {
  bool Success = 1;
  string Message = 2;
}

message GetPagedLocationsRequest {
  int32 PageNumber = 1;
  int32 PageSize = 2;
}

message GetPagedLocationsResponse {
  repeated LocationInfo Locations = 1;
  int32 TotalCount = 2;
  int32 PageNumber = 3;
  int32 PageSize = 4;
}

message GetAllLocationsRequest {
  // This message can be empty or can include filters if needed in the future
}
message GetAllLocationsResponse {
  repeated LocationInfo Locations = 1;  
}

message LocationInfo {
  string LocationId = 1;
  string LocationName = 2;
  string Address = 3;
  string Latitude = 4;
  string Longitude = 5;
  string TimeZone = 6;
  string ContactInfo = 7;
}

