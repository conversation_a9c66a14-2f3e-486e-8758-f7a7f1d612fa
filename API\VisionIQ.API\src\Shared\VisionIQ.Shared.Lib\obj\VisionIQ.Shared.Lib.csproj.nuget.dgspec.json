{"format": 1, "restore": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj": {}}, "projects": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj", "projectName": "VisionIQ.Shared.Lib", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}