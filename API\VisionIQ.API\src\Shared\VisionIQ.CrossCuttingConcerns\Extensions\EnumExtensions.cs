﻿using System.ComponentModel;

namespace VisionIQ.CrossCuttingConcerns.Extensions;

public static partial class Extensions
{
    public static string ToDescriptionString(this Enum val)
    {
        var attributes = (DescriptionAttribute[])val.GetType().GetField(val.ToString())
            ?.GetCustomAttributes(typeof(DescriptionAttribute), false);

        return attributes is { Length: > 0 }
            ? attributes[0].Description
            : val.ToString();
    }

    public static string ToLower(this Enum val)
    {
        var value = val.ToString();

        return value.ToLower();
    }

}