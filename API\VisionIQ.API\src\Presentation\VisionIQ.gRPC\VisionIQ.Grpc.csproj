﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Protos\tag.proto" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\greet.proto" GrpcServices="Server" />
    <Protobuf Include="Protos\location.proto" GrpcServices="Server" />
    <Protobuf Include="Protos\tag.proto" GrpcServices="Server" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Application\VisionIQ.Application.Interfaces\VisionIQ.Interfaces.csproj" />
    <ProjectReference Include="..\..\Infrastructure\VisionIQ.Persistence\VisionIQ.Persistence.csproj" />
    <ProjectReference Include="..\..\Shared\VisionIQ.CrossCuttingConcerns\VisionIQ.CrossCuttingConcerns.csproj" />
    <ProjectReference Include="..\..\Shared\VisionIQ.Shared.Lib\VisionIQ.Shared.Lib.csproj" />
  </ItemGroup>

</Project>
