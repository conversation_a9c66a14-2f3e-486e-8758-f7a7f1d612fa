﻿using VisionIQ.Grpc.Mappers;

namespace VisionIQ.Grpc.Extension
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddPresentation(this IServiceCollection services, IConfiguration config)
        {
            services.AddAutoMapper(cfg => { }, typeof(LocationMappingProfile), typeof(TagMappingProfile));

            services.AddScoped<ILocationMapper, LocationMapper>();
            services.AddScoped<ITagMapper, TagMapper>();

            return services;
        }

    }

}
