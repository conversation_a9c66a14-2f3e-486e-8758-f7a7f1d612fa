﻿using AutoMapper;
using VisionIQ.Grpc.Protos;
using Location = VisionIQ.Domain.Location;

namespace VisionIQ.Grpc.Mappers
{
    public class LocationMapper : ILocationMapper
    {
        private readonly IMapper _mapper;

        public LocationMapper(IMapper mapper)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public Location MapToEntity(CreateLocationRequest request)
        {
            return _mapper.Map<Location>(request);
        }

        public void MapToEntity(UpdateLocationRequest request, Location entity)
        {
            _mapper.Map(request, entity);
        }

        public CreateLocationResponse MapToCreateResponse(Location entity)
        {
            return _mapper.Map<CreateLocationResponse>(entity);
        }

        public GetLocationResponse MapToGetResponse(Location entity)
        {
            return _mapper.Map<GetLocationResponse>(entity);
        }

        public UpdateLocationResponse MapToUpdateResponse(Location entity)
        {
            return _mapper.Map<UpdateLocationResponse>(entity);
        }

        public LocationInfo MapToLocationInfo(Location entity)
        {
            return _mapper.Map<LocationInfo>(entity);
        }
    }

    public interface ILocationMapper
    {
        Location MapToEntity(CreateLocationRequest request);
        void MapToEntity(UpdateLocationRequest request, Location entity);
        CreateLocationResponse MapToCreateResponse(Location entity);
        GetLocationResponse MapToGetResponse(Location entity);
        UpdateLocationResponse MapToUpdateResponse(Location entity);
        LocationInfo MapToLocationInfo(Location entity);
    }
}
