{"format": 1, "restore": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Presentation\\VisionIQ.gRPC\\VisionIQ.Grpc.csproj": {}}, "projects": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj", "projectName": "VisionIQ.Interfaces", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj", "projectName": "VisionIQ.Domain", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj", "projectName": "VisionIQ.Persistence", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj"}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Presentation\\VisionIQ.gRPC\\VisionIQ.Grpc.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Presentation\\VisionIQ.gRPC\\VisionIQ.Grpc.csproj", "projectName": "VisionIQ.Grpc", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Presentation\\VisionIQ.gRPC\\VisionIQ.Grpc.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Presentation\\VisionIQ.gRPC\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Application\\VisionIQ.Application.Interfaces\\VisionIQ.Interfaces.csproj"}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Infrastructure\\VisionIQ.Persistence\\VisionIQ.Persistence.csproj"}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj"}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj", "projectName": "VisionIQ.CrossCuttingConcerns", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\VisionIQ.CrossCuttingConcerns.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.CrossCuttingConcerns\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj": {"projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Domain\\VisionIQ.Domain\\VisionIQ.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj", "projectName": "VisionIQ.Shared.Lib", "projectPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\VisionIQ.Shared.Lib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VisionIQ\\API\\VisionIQ.API\\src\\Shared\\VisionIQ.Shared.Lib\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}