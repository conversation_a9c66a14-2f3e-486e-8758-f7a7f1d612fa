﻿namespace VisionIQ.CrossCuttingConcerns.Helper
{
    using System.Diagnostics.CodeAnalysis;
    using System.Runtime.CompilerServices;

    namespace VisionIQ.gRPC.Services.Guards
    {
        public static class Guard
        {
            public static GuardClause Against { get; } = new GuardClause();
        }

        public class GuardClause
        {
            public T Null<T>(T input, string parameterName, [CallerMemberName] string? callerName = null)
                where T : class
            {
                if (input is null)
                    throw new ArgumentNullException(parameterName, $"Parameter '{parameterName}' cannot be null in {callerName}");

                return input;
            }

            public string NullOrEmpty(string input, string parameterName, [CallerMemberName] string? callerName = null)
            {
                if (string.IsNullOrEmpty(input))
                    throw new ArgumentException($"Parameter '{parameterName}' cannot be null or empty in {callerName}", parameterName);

                return input;
            }

            public int OutOfRange(int input, string parameterName, int min, int max, [CallerMemberName] string? callerName = null)
            {
                if (input < min || input > max)
                    throw new ArgumentOutOfRangeException(parameterName,
                        $"Parameter '{parameterName}' must be between {min} and {max} in {callerName}");

                return input;
            }

            public  string InvalidGuidOrEmpty([NotNull] string input, string parameterName, string message = null)
            {
                if (input == null)
                {
                    throw new ArgumentNullException(parameterName);
                }

                if (string.IsNullOrWhiteSpace(input))
                {
                    throw new ArgumentException($"Input '{parameterName ?? "guidString"}' is not valid format.");
                }

                if (!Guid.TryParse(input, out var guid))
                {
                    throw new ArgumentException($"Input '{parameterName ?? "guidString"}' is not valid format.");
                }

                if (guid == Guid.Empty)
                {
                    throw new ArgumentException($"Input '{parameterName ?? "guidString"}' is not valid format.");
                }

                return input;
            }
        }
    }
}
