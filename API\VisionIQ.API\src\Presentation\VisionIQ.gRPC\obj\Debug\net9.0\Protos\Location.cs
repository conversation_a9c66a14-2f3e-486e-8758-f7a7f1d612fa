// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/location.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace VisionIQ.Grpc.Protos {

  /// <summary>Holder for reflection information generated from Protos/location.proto</summary>
  public static partial class LocationReflection {

    #region Descriptor
    /// <summary>File descriptor for Protos/location.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static LocationReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVQcm90b3MvbG9jYXRpb24ucHJvdG8iKAoSR2V0TG9jYXRpb25SZXF1ZXN0",
            "EhIKCkxvY2F0aW9uSWQYASABKAkidgoTR2V0TG9jYXRpb25SZXNwb25zZRIS",
            "CgpMb2NhdGlvbklkGAEgASgJEhQKDExvY2F0aW9uTmFtZRgCIAEoCRIQCghM",
            "YXRpdHVkZRgDIAEoCRIRCglMb25naXR1ZGUYBCABKAkSEAoIVGltZVpvbmUY",
            "BSABKAkinQEKFUNyZWF0ZUxvY2F0aW9uUmVxdWVzdBIUCgxMb2NhdGlvbk5h",
            "bWUYASABKAkSDwoHQWRkcmVzcxgCIAEoCRIQCghMYXRpdHVkZRgDIAEoCRIR",
            "CglMb25naXR1ZGUYBCABKAkSEAoIVGltZVpvbmUYBSABKAkSEwoLQ29udGFj",
            "dEluZm8YBiABKAkSEQoJQ3JlYXRlZEJ5GAcgASgJIrIBChZDcmVhdGVMb2Nh",
            "dGlvblJlc3BvbnNlEhIKCkxvY2F0aW9uSWQYASABKAkSFAoMTG9jYXRpb25O",
            "YW1lGAIgASgJEg8KB0FkZHJlc3MYAyABKAkSEAoITGF0aXR1ZGUYBCABKAkS",
            "EQoJTG9uZ2l0dWRlGAUgASgJEhAKCFRpbWVab25lGAYgASgJEhMKC0NvbnRh",
            "Y3RJbmZvGAcgASgJEhEKCUNyZWF0ZWRCeRgIIAEoCSKxAQoVVXBkYXRlTG9j",
            "YXRpb25SZXF1ZXN0EhIKCkxvY2F0aW9uSWQYASABKAkSFAoMTG9jYXRpb25O",
            "YW1lGAIgASgJEg8KB0FkZHJlc3MYAyABKAkSEAoITGF0aXR1ZGUYBCABKAkS",
            "EQoJTG9uZ2l0dWRlGAUgASgJEhAKCFRpbWVab25lGAYgASgJEhMKC0NvbnRh",
            "Y3RJbmZvGAcgASgJEhEKCVVwZGF0ZWRCeRgIIAEoCSKyAQoWVXBkYXRlTG9j",
            "YXRpb25SZXNwb25zZRISCgpMb2NhdGlvbklkGAEgASgJEhQKDExvY2F0aW9u",
            "TmFtZRgCIAEoCRIPCgdBZGRyZXNzGAMgASgJEhAKCExhdGl0dWRlGAQgASgJ",
            "EhEKCUxvbmdpdHVkZRgFIAEoCRIQCghUaW1lWm9uZRgGIAEoCRITCgtDb250",
            "YWN0SW5mbxgHIAEoCRIRCglVcGRhdGVkQnkYCCABKAkiPgoVRGVsZXRlTG9j",
            "YXRpb25SZXF1ZXN0EhIKCkxvY2F0aW9uSWQYASABKAkSEQoJRGVsZXRlZEJ5",
            "GAIgASgJIjoKFkRlbGV0ZUxvY2F0aW9uUmVzcG9uc2USDwoHU3VjY2VzcxgB",
            "IAEoCBIPCgdNZXNzYWdlGAIgASgJIkAKGEdldFBhZ2VkTG9jYXRpb25zUmVx",
            "dWVzdBISCgpQYWdlTnVtYmVyGAEgASgFEhAKCFBhZ2VTaXplGAIgASgFIncK",
            "GUdldFBhZ2VkTG9jYXRpb25zUmVzcG9uc2USIAoJTG9jYXRpb25zGAEgAygL",
            "Mg0uTG9jYXRpb25JbmZvEhIKClRvdGFsQ291bnQYAiABKAUSEgoKUGFnZU51",
            "bWJlchgDIAEoBRIQCghQYWdlU2l6ZRgEIAEoBSIYChZHZXRBbGxMb2NhdGlv",
            "bnNSZXF1ZXN0IjsKF0dldEFsbExvY2F0aW9uc1Jlc3BvbnNlEiAKCUxvY2F0",
            "aW9ucxgBIAMoCzINLkxvY2F0aW9uSW5mbyKVAQoMTG9jYXRpb25JbmZvEhIK",
            "CkxvY2F0aW9uSWQYASABKAkSFAoMTG9jYXRpb25OYW1lGAIgASgJEg8KB0Fk",
            "ZHJlc3MYAyABKAkSEAoITGF0aXR1ZGUYBCABKAkSEQoJTG9uZ2l0dWRlGAUg",
            "ASgJEhAKCFRpbWVab25lGAYgASgJEhMKC0NvbnRhY3RJbmZvGAcgASgJMqQD",
            "CghMb2NhdGlvbhJBCg5DcmVhdGVMb2NhdGlvbhIWLkNyZWF0ZUxvY2F0aW9u",
            "UmVxdWVzdBoXLkNyZWF0ZUxvY2F0aW9uUmVzcG9uc2USQQoOVXBkYXRlTG9j",
            "YXRpb24SFi5VcGRhdGVMb2NhdGlvblJlcXVlc3QaFy5VcGRhdGVMb2NhdGlv",
            "blJlc3BvbnNlEkEKDkRlbGV0ZUxvY2F0aW9uEhYuRGVsZXRlTG9jYXRpb25S",
            "ZXF1ZXN0GhcuRGVsZXRlTG9jYXRpb25SZXNwb25zZRI4CgtHZXRMb2NhdGlv",
            "bhITLkdldExvY2F0aW9uUmVxdWVzdBoULkdldExvY2F0aW9uUmVzcG9uc2US",
            "RAoPR2V0QWxsTG9jYXRpb25zEhcuR2V0QWxsTG9jYXRpb25zUmVxdWVzdBoY",
            "LkdldEFsbExvY2F0aW9uc1Jlc3BvbnNlEk8KFkdldFBhZ2VkTG9jYXRpb25z",
            "QXN5bmMSGS5HZXRQYWdlZExvY2F0aW9uc1JlcXVlc3QaGi5HZXRQYWdlZExv",
            "Y2F0aW9uc1Jlc3BvbnNlQheqAhRWaXNpb25JUS5HcnBjLlByb3Rvc2IGcHJv",
            "dG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.GetLocationRequest), global::VisionIQ.Grpc.Protos.GetLocationRequest.Parser, new[]{ "LocationId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.GetLocationResponse), global::VisionIQ.Grpc.Protos.GetLocationResponse.Parser, new[]{ "LocationId", "LocationName", "Latitude", "Longitude", "TimeZone" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.CreateLocationRequest), global::VisionIQ.Grpc.Protos.CreateLocationRequest.Parser, new[]{ "LocationName", "Address", "Latitude", "Longitude", "TimeZone", "ContactInfo", "CreatedBy" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.CreateLocationResponse), global::VisionIQ.Grpc.Protos.CreateLocationResponse.Parser, new[]{ "LocationId", "LocationName", "Address", "Latitude", "Longitude", "TimeZone", "ContactInfo", "CreatedBy" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.UpdateLocationRequest), global::VisionIQ.Grpc.Protos.UpdateLocationRequest.Parser, new[]{ "LocationId", "LocationName", "Address", "Latitude", "Longitude", "TimeZone", "ContactInfo", "UpdatedBy" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.UpdateLocationResponse), global::VisionIQ.Grpc.Protos.UpdateLocationResponse.Parser, new[]{ "LocationId", "LocationName", "Address", "Latitude", "Longitude", "TimeZone", "ContactInfo", "UpdatedBy" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.DeleteLocationRequest), global::VisionIQ.Grpc.Protos.DeleteLocationRequest.Parser, new[]{ "LocationId", "DeletedBy" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.DeleteLocationResponse), global::VisionIQ.Grpc.Protos.DeleteLocationResponse.Parser, new[]{ "Success", "Message" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest), global::VisionIQ.Grpc.Protos.GetPagedLocationsRequest.Parser, new[]{ "PageNumber", "PageSize" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse), global::VisionIQ.Grpc.Protos.GetPagedLocationsResponse.Parser, new[]{ "Locations", "TotalCount", "PageNumber", "PageSize" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.GetAllLocationsRequest), global::VisionIQ.Grpc.Protos.GetAllLocationsRequest.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.GetAllLocationsResponse), global::VisionIQ.Grpc.Protos.GetAllLocationsResponse.Parser, new[]{ "Locations" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::VisionIQ.Grpc.Protos.LocationInfo), global::VisionIQ.Grpc.Protos.LocationInfo.Parser, new[]{ "LocationId", "LocationName", "Address", "Latitude", "Longitude", "TimeZone", "ContactInfo" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetLocationRequest : pb::IMessage<GetLocationRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetLocationRequest> _parser = new pb::MessageParser<GetLocationRequest>(() => new GetLocationRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetLocationRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetLocationRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetLocationRequest(GetLocationRequest other) : this() {
      locationId_ = other.locationId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetLocationRequest Clone() {
      return new GetLocationRequest(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetLocationRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetLocationRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetLocationRequest other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetLocationResponse : pb::IMessage<GetLocationResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetLocationResponse> _parser = new pb::MessageParser<GetLocationResponse>(() => new GetLocationResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetLocationResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetLocationResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetLocationResponse(GetLocationResponse other) : this() {
      locationId_ = other.locationId_;
      locationName_ = other.locationName_;
      latitude_ = other.latitude_;
      longitude_ = other.longitude_;
      timeZone_ = other.timeZone_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetLocationResponse Clone() {
      return new GetLocationResponse(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LocationName" field.</summary>
    public const int LocationNameFieldNumber = 2;
    private string locationName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationName {
      get { return locationName_; }
      set {
        locationName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Latitude" field.</summary>
    public const int LatitudeFieldNumber = 3;
    private string latitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Latitude {
      get { return latitude_; }
      set {
        latitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Longitude" field.</summary>
    public const int LongitudeFieldNumber = 4;
    private string longitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Longitude {
      get { return longitude_; }
      set {
        longitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TimeZone" field.</summary>
    public const int TimeZoneFieldNumber = 5;
    private string timeZone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TimeZone {
      get { return timeZone_; }
      set {
        timeZone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetLocationResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetLocationResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      if (LocationName != other.LocationName) return false;
      if (Latitude != other.Latitude) return false;
      if (Longitude != other.Longitude) return false;
      if (TimeZone != other.TimeZone) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (LocationName.Length != 0) hash ^= LocationName.GetHashCode();
      if (Latitude.Length != 0) hash ^= Latitude.GetHashCode();
      if (Longitude.Length != 0) hash ^= Longitude.GetHashCode();
      if (TimeZone.Length != 0) hash ^= TimeZone.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(TimeZone);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(TimeZone);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (LocationName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationName);
      }
      if (Latitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Latitude);
      }
      if (Longitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Longitude);
      }
      if (TimeZone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TimeZone);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetLocationResponse other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      if (other.LocationName.Length != 0) {
        LocationName = other.LocationName;
      }
      if (other.Latitude.Length != 0) {
        Latitude = other.Latitude;
      }
      if (other.Longitude.Length != 0) {
        Longitude = other.Longitude;
      }
      if (other.TimeZone.Length != 0) {
        TimeZone = other.TimeZone;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Latitude = input.ReadString();
            break;
          }
          case 34: {
            Longitude = input.ReadString();
            break;
          }
          case 42: {
            TimeZone = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Latitude = input.ReadString();
            break;
          }
          case 34: {
            Longitude = input.ReadString();
            break;
          }
          case 42: {
            TimeZone = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CreateLocationRequest : pb::IMessage<CreateLocationRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CreateLocationRequest> _parser = new pb::MessageParser<CreateLocationRequest>(() => new CreateLocationRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CreateLocationRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateLocationRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateLocationRequest(CreateLocationRequest other) : this() {
      locationName_ = other.locationName_;
      address_ = other.address_;
      latitude_ = other.latitude_;
      longitude_ = other.longitude_;
      timeZone_ = other.timeZone_;
      contactInfo_ = other.contactInfo_;
      createdBy_ = other.createdBy_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateLocationRequest Clone() {
      return new CreateLocationRequest(this);
    }

    /// <summary>Field number for the "LocationName" field.</summary>
    public const int LocationNameFieldNumber = 1;
    private string locationName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationName {
      get { return locationName_; }
      set {
        locationName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Address" field.</summary>
    public const int AddressFieldNumber = 2;
    private string address_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Address {
      get { return address_; }
      set {
        address_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Latitude" field.</summary>
    public const int LatitudeFieldNumber = 3;
    private string latitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Latitude {
      get { return latitude_; }
      set {
        latitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Longitude" field.</summary>
    public const int LongitudeFieldNumber = 4;
    private string longitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Longitude {
      get { return longitude_; }
      set {
        longitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TimeZone" field.</summary>
    public const int TimeZoneFieldNumber = 5;
    private string timeZone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TimeZone {
      get { return timeZone_; }
      set {
        timeZone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "ContactInfo" field.</summary>
    public const int ContactInfoFieldNumber = 6;
    private string contactInfo_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ContactInfo {
      get { return contactInfo_; }
      set {
        contactInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "CreatedBy" field.</summary>
    public const int CreatedByFieldNumber = 7;
    private string createdBy_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string CreatedBy {
      get { return createdBy_; }
      set {
        createdBy_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CreateLocationRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CreateLocationRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationName != other.LocationName) return false;
      if (Address != other.Address) return false;
      if (Latitude != other.Latitude) return false;
      if (Longitude != other.Longitude) return false;
      if (TimeZone != other.TimeZone) return false;
      if (ContactInfo != other.ContactInfo) return false;
      if (CreatedBy != other.CreatedBy) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationName.Length != 0) hash ^= LocationName.GetHashCode();
      if (Address.Length != 0) hash ^= Address.GetHashCode();
      if (Latitude.Length != 0) hash ^= Latitude.GetHashCode();
      if (Longitude.Length != 0) hash ^= Longitude.GetHashCode();
      if (TimeZone.Length != 0) hash ^= TimeZone.GetHashCode();
      if (ContactInfo.Length != 0) hash ^= ContactInfo.GetHashCode();
      if (CreatedBy.Length != 0) hash ^= CreatedBy.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(ContactInfo);
      }
      if (CreatedBy.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(CreatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(ContactInfo);
      }
      if (CreatedBy.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(CreatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationName);
      }
      if (Address.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Address);
      }
      if (Latitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Latitude);
      }
      if (Longitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Longitude);
      }
      if (TimeZone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ContactInfo);
      }
      if (CreatedBy.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CreatedBy);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CreateLocationRequest other) {
      if (other == null) {
        return;
      }
      if (other.LocationName.Length != 0) {
        LocationName = other.LocationName;
      }
      if (other.Address.Length != 0) {
        Address = other.Address;
      }
      if (other.Latitude.Length != 0) {
        Latitude = other.Latitude;
      }
      if (other.Longitude.Length != 0) {
        Longitude = other.Longitude;
      }
      if (other.TimeZone.Length != 0) {
        TimeZone = other.TimeZone;
      }
      if (other.ContactInfo.Length != 0) {
        ContactInfo = other.ContactInfo;
      }
      if (other.CreatedBy.Length != 0) {
        CreatedBy = other.CreatedBy;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationName = input.ReadString();
            break;
          }
          case 18: {
            Address = input.ReadString();
            break;
          }
          case 26: {
            Latitude = input.ReadString();
            break;
          }
          case 34: {
            Longitude = input.ReadString();
            break;
          }
          case 42: {
            TimeZone = input.ReadString();
            break;
          }
          case 50: {
            ContactInfo = input.ReadString();
            break;
          }
          case 58: {
            CreatedBy = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationName = input.ReadString();
            break;
          }
          case 18: {
            Address = input.ReadString();
            break;
          }
          case 26: {
            Latitude = input.ReadString();
            break;
          }
          case 34: {
            Longitude = input.ReadString();
            break;
          }
          case 42: {
            TimeZone = input.ReadString();
            break;
          }
          case 50: {
            ContactInfo = input.ReadString();
            break;
          }
          case 58: {
            CreatedBy = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CreateLocationResponse : pb::IMessage<CreateLocationResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CreateLocationResponse> _parser = new pb::MessageParser<CreateLocationResponse>(() => new CreateLocationResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CreateLocationResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateLocationResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateLocationResponse(CreateLocationResponse other) : this() {
      locationId_ = other.locationId_;
      locationName_ = other.locationName_;
      address_ = other.address_;
      latitude_ = other.latitude_;
      longitude_ = other.longitude_;
      timeZone_ = other.timeZone_;
      contactInfo_ = other.contactInfo_;
      createdBy_ = other.createdBy_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateLocationResponse Clone() {
      return new CreateLocationResponse(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LocationName" field.</summary>
    public const int LocationNameFieldNumber = 2;
    private string locationName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationName {
      get { return locationName_; }
      set {
        locationName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Address" field.</summary>
    public const int AddressFieldNumber = 3;
    private string address_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Address {
      get { return address_; }
      set {
        address_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Latitude" field.</summary>
    public const int LatitudeFieldNumber = 4;
    private string latitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Latitude {
      get { return latitude_; }
      set {
        latitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Longitude" field.</summary>
    public const int LongitudeFieldNumber = 5;
    private string longitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Longitude {
      get { return longitude_; }
      set {
        longitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TimeZone" field.</summary>
    public const int TimeZoneFieldNumber = 6;
    private string timeZone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TimeZone {
      get { return timeZone_; }
      set {
        timeZone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "ContactInfo" field.</summary>
    public const int ContactInfoFieldNumber = 7;
    private string contactInfo_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ContactInfo {
      get { return contactInfo_; }
      set {
        contactInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "CreatedBy" field.</summary>
    public const int CreatedByFieldNumber = 8;
    private string createdBy_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string CreatedBy {
      get { return createdBy_; }
      set {
        createdBy_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CreateLocationResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CreateLocationResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      if (LocationName != other.LocationName) return false;
      if (Address != other.Address) return false;
      if (Latitude != other.Latitude) return false;
      if (Longitude != other.Longitude) return false;
      if (TimeZone != other.TimeZone) return false;
      if (ContactInfo != other.ContactInfo) return false;
      if (CreatedBy != other.CreatedBy) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (LocationName.Length != 0) hash ^= LocationName.GetHashCode();
      if (Address.Length != 0) hash ^= Address.GetHashCode();
      if (Latitude.Length != 0) hash ^= Latitude.GetHashCode();
      if (Longitude.Length != 0) hash ^= Longitude.GetHashCode();
      if (TimeZone.Length != 0) hash ^= TimeZone.GetHashCode();
      if (ContactInfo.Length != 0) hash ^= ContactInfo.GetHashCode();
      if (CreatedBy.Length != 0) hash ^= CreatedBy.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (CreatedBy.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(CreatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (CreatedBy.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(CreatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (LocationName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationName);
      }
      if (Address.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Address);
      }
      if (Latitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Latitude);
      }
      if (Longitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Longitude);
      }
      if (TimeZone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ContactInfo);
      }
      if (CreatedBy.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CreatedBy);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CreateLocationResponse other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      if (other.LocationName.Length != 0) {
        LocationName = other.LocationName;
      }
      if (other.Address.Length != 0) {
        Address = other.Address;
      }
      if (other.Latitude.Length != 0) {
        Latitude = other.Latitude;
      }
      if (other.Longitude.Length != 0) {
        Longitude = other.Longitude;
      }
      if (other.TimeZone.Length != 0) {
        TimeZone = other.TimeZone;
      }
      if (other.ContactInfo.Length != 0) {
        ContactInfo = other.ContactInfo;
      }
      if (other.CreatedBy.Length != 0) {
        CreatedBy = other.CreatedBy;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
          case 66: {
            CreatedBy = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
          case 66: {
            CreatedBy = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UpdateLocationRequest : pb::IMessage<UpdateLocationRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UpdateLocationRequest> _parser = new pb::MessageParser<UpdateLocationRequest>(() => new UpdateLocationRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UpdateLocationRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpdateLocationRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpdateLocationRequest(UpdateLocationRequest other) : this() {
      locationId_ = other.locationId_;
      locationName_ = other.locationName_;
      address_ = other.address_;
      latitude_ = other.latitude_;
      longitude_ = other.longitude_;
      timeZone_ = other.timeZone_;
      contactInfo_ = other.contactInfo_;
      updatedBy_ = other.updatedBy_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpdateLocationRequest Clone() {
      return new UpdateLocationRequest(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LocationName" field.</summary>
    public const int LocationNameFieldNumber = 2;
    private string locationName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationName {
      get { return locationName_; }
      set {
        locationName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Address" field.</summary>
    public const int AddressFieldNumber = 3;
    private string address_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Address {
      get { return address_; }
      set {
        address_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Latitude" field.</summary>
    public const int LatitudeFieldNumber = 4;
    private string latitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Latitude {
      get { return latitude_; }
      set {
        latitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Longitude" field.</summary>
    public const int LongitudeFieldNumber = 5;
    private string longitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Longitude {
      get { return longitude_; }
      set {
        longitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TimeZone" field.</summary>
    public const int TimeZoneFieldNumber = 6;
    private string timeZone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TimeZone {
      get { return timeZone_; }
      set {
        timeZone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "ContactInfo" field.</summary>
    public const int ContactInfoFieldNumber = 7;
    private string contactInfo_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ContactInfo {
      get { return contactInfo_; }
      set {
        contactInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "UpdatedBy" field.</summary>
    public const int UpdatedByFieldNumber = 8;
    private string updatedBy_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UpdatedBy {
      get { return updatedBy_; }
      set {
        updatedBy_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UpdateLocationRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UpdateLocationRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      if (LocationName != other.LocationName) return false;
      if (Address != other.Address) return false;
      if (Latitude != other.Latitude) return false;
      if (Longitude != other.Longitude) return false;
      if (TimeZone != other.TimeZone) return false;
      if (ContactInfo != other.ContactInfo) return false;
      if (UpdatedBy != other.UpdatedBy) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (LocationName.Length != 0) hash ^= LocationName.GetHashCode();
      if (Address.Length != 0) hash ^= Address.GetHashCode();
      if (Latitude.Length != 0) hash ^= Latitude.GetHashCode();
      if (Longitude.Length != 0) hash ^= Longitude.GetHashCode();
      if (TimeZone.Length != 0) hash ^= TimeZone.GetHashCode();
      if (ContactInfo.Length != 0) hash ^= ContactInfo.GetHashCode();
      if (UpdatedBy.Length != 0) hash ^= UpdatedBy.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (UpdatedBy.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(UpdatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (UpdatedBy.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(UpdatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (LocationName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationName);
      }
      if (Address.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Address);
      }
      if (Latitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Latitude);
      }
      if (Longitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Longitude);
      }
      if (TimeZone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ContactInfo);
      }
      if (UpdatedBy.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UpdatedBy);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UpdateLocationRequest other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      if (other.LocationName.Length != 0) {
        LocationName = other.LocationName;
      }
      if (other.Address.Length != 0) {
        Address = other.Address;
      }
      if (other.Latitude.Length != 0) {
        Latitude = other.Latitude;
      }
      if (other.Longitude.Length != 0) {
        Longitude = other.Longitude;
      }
      if (other.TimeZone.Length != 0) {
        TimeZone = other.TimeZone;
      }
      if (other.ContactInfo.Length != 0) {
        ContactInfo = other.ContactInfo;
      }
      if (other.UpdatedBy.Length != 0) {
        UpdatedBy = other.UpdatedBy;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
          case 66: {
            UpdatedBy = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
          case 66: {
            UpdatedBy = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UpdateLocationResponse : pb::IMessage<UpdateLocationResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UpdateLocationResponse> _parser = new pb::MessageParser<UpdateLocationResponse>(() => new UpdateLocationResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UpdateLocationResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpdateLocationResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpdateLocationResponse(UpdateLocationResponse other) : this() {
      locationId_ = other.locationId_;
      locationName_ = other.locationName_;
      address_ = other.address_;
      latitude_ = other.latitude_;
      longitude_ = other.longitude_;
      timeZone_ = other.timeZone_;
      contactInfo_ = other.contactInfo_;
      updatedBy_ = other.updatedBy_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpdateLocationResponse Clone() {
      return new UpdateLocationResponse(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LocationName" field.</summary>
    public const int LocationNameFieldNumber = 2;
    private string locationName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationName {
      get { return locationName_; }
      set {
        locationName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Address" field.</summary>
    public const int AddressFieldNumber = 3;
    private string address_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Address {
      get { return address_; }
      set {
        address_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Latitude" field.</summary>
    public const int LatitudeFieldNumber = 4;
    private string latitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Latitude {
      get { return latitude_; }
      set {
        latitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Longitude" field.</summary>
    public const int LongitudeFieldNumber = 5;
    private string longitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Longitude {
      get { return longitude_; }
      set {
        longitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TimeZone" field.</summary>
    public const int TimeZoneFieldNumber = 6;
    private string timeZone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TimeZone {
      get { return timeZone_; }
      set {
        timeZone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "ContactInfo" field.</summary>
    public const int ContactInfoFieldNumber = 7;
    private string contactInfo_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ContactInfo {
      get { return contactInfo_; }
      set {
        contactInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "UpdatedBy" field.</summary>
    public const int UpdatedByFieldNumber = 8;
    private string updatedBy_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UpdatedBy {
      get { return updatedBy_; }
      set {
        updatedBy_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UpdateLocationResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UpdateLocationResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      if (LocationName != other.LocationName) return false;
      if (Address != other.Address) return false;
      if (Latitude != other.Latitude) return false;
      if (Longitude != other.Longitude) return false;
      if (TimeZone != other.TimeZone) return false;
      if (ContactInfo != other.ContactInfo) return false;
      if (UpdatedBy != other.UpdatedBy) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (LocationName.Length != 0) hash ^= LocationName.GetHashCode();
      if (Address.Length != 0) hash ^= Address.GetHashCode();
      if (Latitude.Length != 0) hash ^= Latitude.GetHashCode();
      if (Longitude.Length != 0) hash ^= Longitude.GetHashCode();
      if (TimeZone.Length != 0) hash ^= TimeZone.GetHashCode();
      if (ContactInfo.Length != 0) hash ^= ContactInfo.GetHashCode();
      if (UpdatedBy.Length != 0) hash ^= UpdatedBy.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (UpdatedBy.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(UpdatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (UpdatedBy.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(UpdatedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (LocationName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationName);
      }
      if (Address.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Address);
      }
      if (Latitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Latitude);
      }
      if (Longitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Longitude);
      }
      if (TimeZone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ContactInfo);
      }
      if (UpdatedBy.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UpdatedBy);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UpdateLocationResponse other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      if (other.LocationName.Length != 0) {
        LocationName = other.LocationName;
      }
      if (other.Address.Length != 0) {
        Address = other.Address;
      }
      if (other.Latitude.Length != 0) {
        Latitude = other.Latitude;
      }
      if (other.Longitude.Length != 0) {
        Longitude = other.Longitude;
      }
      if (other.TimeZone.Length != 0) {
        TimeZone = other.TimeZone;
      }
      if (other.ContactInfo.Length != 0) {
        ContactInfo = other.ContactInfo;
      }
      if (other.UpdatedBy.Length != 0) {
        UpdatedBy = other.UpdatedBy;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
          case 66: {
            UpdatedBy = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
          case 66: {
            UpdatedBy = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class DeleteLocationRequest : pb::IMessage<DeleteLocationRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DeleteLocationRequest> _parser = new pb::MessageParser<DeleteLocationRequest>(() => new DeleteLocationRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DeleteLocationRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteLocationRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteLocationRequest(DeleteLocationRequest other) : this() {
      locationId_ = other.locationId_;
      deletedBy_ = other.deletedBy_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteLocationRequest Clone() {
      return new DeleteLocationRequest(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "DeletedBy" field.</summary>
    public const int DeletedByFieldNumber = 2;
    private string deletedBy_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string DeletedBy {
      get { return deletedBy_; }
      set {
        deletedBy_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DeleteLocationRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DeleteLocationRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      if (DeletedBy != other.DeletedBy) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (DeletedBy.Length != 0) hash ^= DeletedBy.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (DeletedBy.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(DeletedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (DeletedBy.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(DeletedBy);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (DeletedBy.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DeletedBy);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DeleteLocationRequest other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      if (other.DeletedBy.Length != 0) {
        DeletedBy = other.DeletedBy;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            DeletedBy = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            DeletedBy = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class DeleteLocationResponse : pb::IMessage<DeleteLocationResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DeleteLocationResponse> _parser = new pb::MessageParser<DeleteLocationResponse>(() => new DeleteLocationResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DeleteLocationResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteLocationResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteLocationResponse(DeleteLocationResponse other) : this() {
      success_ = other.success_;
      message_ = other.message_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteLocationResponse Clone() {
      return new DeleteLocationResponse(this);
    }

    /// <summary>Field number for the "Success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "Message" field.</summary>
    public const int MessageFieldNumber = 2;
    private string message_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Message {
      get { return message_; }
      set {
        message_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DeleteLocationResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DeleteLocationResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (Message != other.Message) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (Message.Length != 0) hash ^= Message.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (Message.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Message);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (Message.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Message);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (Message.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Message);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DeleteLocationResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.Message.Length != 0) {
        Message = other.Message;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            Message = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            Message = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetPagedLocationsRequest : pb::IMessage<GetPagedLocationsRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetPagedLocationsRequest> _parser = new pb::MessageParser<GetPagedLocationsRequest>(() => new GetPagedLocationsRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetPagedLocationsRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPagedLocationsRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPagedLocationsRequest(GetPagedLocationsRequest other) : this() {
      pageNumber_ = other.pageNumber_;
      pageSize_ = other.pageSize_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPagedLocationsRequest Clone() {
      return new GetPagedLocationsRequest(this);
    }

    /// <summary>Field number for the "PageNumber" field.</summary>
    public const int PageNumberFieldNumber = 1;
    private int pageNumber_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PageNumber {
      get { return pageNumber_; }
      set {
        pageNumber_ = value;
      }
    }

    /// <summary>Field number for the "PageSize" field.</summary>
    public const int PageSizeFieldNumber = 2;
    private int pageSize_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PageSize {
      get { return pageSize_; }
      set {
        pageSize_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetPagedLocationsRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetPagedLocationsRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (PageNumber != other.PageNumber) return false;
      if (PageSize != other.PageSize) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (PageNumber != 0) hash ^= PageNumber.GetHashCode();
      if (PageSize != 0) hash ^= PageSize.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (PageNumber != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(PageNumber);
      }
      if (PageSize != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(PageSize);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (PageNumber != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(PageNumber);
      }
      if (PageSize != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(PageSize);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (PageNumber != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PageNumber);
      }
      if (PageSize != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PageSize);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetPagedLocationsRequest other) {
      if (other == null) {
        return;
      }
      if (other.PageNumber != 0) {
        PageNumber = other.PageNumber;
      }
      if (other.PageSize != 0) {
        PageSize = other.PageSize;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            PageNumber = input.ReadInt32();
            break;
          }
          case 16: {
            PageSize = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            PageNumber = input.ReadInt32();
            break;
          }
          case 16: {
            PageSize = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetPagedLocationsResponse : pb::IMessage<GetPagedLocationsResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetPagedLocationsResponse> _parser = new pb::MessageParser<GetPagedLocationsResponse>(() => new GetPagedLocationsResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetPagedLocationsResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPagedLocationsResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPagedLocationsResponse(GetPagedLocationsResponse other) : this() {
      locations_ = other.locations_.Clone();
      totalCount_ = other.totalCount_;
      pageNumber_ = other.pageNumber_;
      pageSize_ = other.pageSize_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPagedLocationsResponse Clone() {
      return new GetPagedLocationsResponse(this);
    }

    /// <summary>Field number for the "Locations" field.</summary>
    public const int LocationsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::VisionIQ.Grpc.Protos.LocationInfo> _repeated_locations_codec
        = pb::FieldCodec.ForMessage(10, global::VisionIQ.Grpc.Protos.LocationInfo.Parser);
    private readonly pbc::RepeatedField<global::VisionIQ.Grpc.Protos.LocationInfo> locations_ = new pbc::RepeatedField<global::VisionIQ.Grpc.Protos.LocationInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::VisionIQ.Grpc.Protos.LocationInfo> Locations {
      get { return locations_; }
    }

    /// <summary>Field number for the "TotalCount" field.</summary>
    public const int TotalCountFieldNumber = 2;
    private int totalCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TotalCount {
      get { return totalCount_; }
      set {
        totalCount_ = value;
      }
    }

    /// <summary>Field number for the "PageNumber" field.</summary>
    public const int PageNumberFieldNumber = 3;
    private int pageNumber_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PageNumber {
      get { return pageNumber_; }
      set {
        pageNumber_ = value;
      }
    }

    /// <summary>Field number for the "PageSize" field.</summary>
    public const int PageSizeFieldNumber = 4;
    private int pageSize_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PageSize {
      get { return pageSize_; }
      set {
        pageSize_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetPagedLocationsResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetPagedLocationsResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!locations_.Equals(other.locations_)) return false;
      if (TotalCount != other.TotalCount) return false;
      if (PageNumber != other.PageNumber) return false;
      if (PageSize != other.PageSize) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= locations_.GetHashCode();
      if (TotalCount != 0) hash ^= TotalCount.GetHashCode();
      if (PageNumber != 0) hash ^= PageNumber.GetHashCode();
      if (PageSize != 0) hash ^= PageSize.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      locations_.WriteTo(output, _repeated_locations_codec);
      if (TotalCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TotalCount);
      }
      if (PageNumber != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(PageNumber);
      }
      if (PageSize != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(PageSize);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      locations_.WriteTo(ref output, _repeated_locations_codec);
      if (TotalCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TotalCount);
      }
      if (PageNumber != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(PageNumber);
      }
      if (PageSize != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(PageSize);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += locations_.CalculateSize(_repeated_locations_codec);
      if (TotalCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TotalCount);
      }
      if (PageNumber != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PageNumber);
      }
      if (PageSize != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PageSize);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetPagedLocationsResponse other) {
      if (other == null) {
        return;
      }
      locations_.Add(other.locations_);
      if (other.TotalCount != 0) {
        TotalCount = other.TotalCount;
      }
      if (other.PageNumber != 0) {
        PageNumber = other.PageNumber;
      }
      if (other.PageSize != 0) {
        PageSize = other.PageSize;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            locations_.AddEntriesFrom(input, _repeated_locations_codec);
            break;
          }
          case 16: {
            TotalCount = input.ReadInt32();
            break;
          }
          case 24: {
            PageNumber = input.ReadInt32();
            break;
          }
          case 32: {
            PageSize = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            locations_.AddEntriesFrom(ref input, _repeated_locations_codec);
            break;
          }
          case 16: {
            TotalCount = input.ReadInt32();
            break;
          }
          case 24: {
            PageNumber = input.ReadInt32();
            break;
          }
          case 32: {
            PageSize = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// This message can be empty or can include filters if needed in the future
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetAllLocationsRequest : pb::IMessage<GetAllLocationsRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetAllLocationsRequest> _parser = new pb::MessageParser<GetAllLocationsRequest>(() => new GetAllLocationsRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetAllLocationsRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAllLocationsRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAllLocationsRequest(GetAllLocationsRequest other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAllLocationsRequest Clone() {
      return new GetAllLocationsRequest(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetAllLocationsRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetAllLocationsRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetAllLocationsRequest other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetAllLocationsResponse : pb::IMessage<GetAllLocationsResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetAllLocationsResponse> _parser = new pb::MessageParser<GetAllLocationsResponse>(() => new GetAllLocationsResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetAllLocationsResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAllLocationsResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAllLocationsResponse(GetAllLocationsResponse other) : this() {
      locations_ = other.locations_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAllLocationsResponse Clone() {
      return new GetAllLocationsResponse(this);
    }

    /// <summary>Field number for the "Locations" field.</summary>
    public const int LocationsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::VisionIQ.Grpc.Protos.LocationInfo> _repeated_locations_codec
        = pb::FieldCodec.ForMessage(10, global::VisionIQ.Grpc.Protos.LocationInfo.Parser);
    private readonly pbc::RepeatedField<global::VisionIQ.Grpc.Protos.LocationInfo> locations_ = new pbc::RepeatedField<global::VisionIQ.Grpc.Protos.LocationInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::VisionIQ.Grpc.Protos.LocationInfo> Locations {
      get { return locations_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetAllLocationsResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetAllLocationsResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!locations_.Equals(other.locations_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= locations_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      locations_.WriteTo(output, _repeated_locations_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      locations_.WriteTo(ref output, _repeated_locations_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += locations_.CalculateSize(_repeated_locations_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetAllLocationsResponse other) {
      if (other == null) {
        return;
      }
      locations_.Add(other.locations_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            locations_.AddEntriesFrom(input, _repeated_locations_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            locations_.AddEntriesFrom(ref input, _repeated_locations_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class LocationInfo : pb::IMessage<LocationInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<LocationInfo> _parser = new pb::MessageParser<LocationInfo>(() => new LocationInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<LocationInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::VisionIQ.Grpc.Protos.LocationReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LocationInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LocationInfo(LocationInfo other) : this() {
      locationId_ = other.locationId_;
      locationName_ = other.locationName_;
      address_ = other.address_;
      latitude_ = other.latitude_;
      longitude_ = other.longitude_;
      timeZone_ = other.timeZone_;
      contactInfo_ = other.contactInfo_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LocationInfo Clone() {
      return new LocationInfo(this);
    }

    /// <summary>Field number for the "LocationId" field.</summary>
    public const int LocationIdFieldNumber = 1;
    private string locationId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationId {
      get { return locationId_; }
      set {
        locationId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LocationName" field.</summary>
    public const int LocationNameFieldNumber = 2;
    private string locationName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LocationName {
      get { return locationName_; }
      set {
        locationName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Address" field.</summary>
    public const int AddressFieldNumber = 3;
    private string address_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Address {
      get { return address_; }
      set {
        address_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Latitude" field.</summary>
    public const int LatitudeFieldNumber = 4;
    private string latitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Latitude {
      get { return latitude_; }
      set {
        latitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Longitude" field.</summary>
    public const int LongitudeFieldNumber = 5;
    private string longitude_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Longitude {
      get { return longitude_; }
      set {
        longitude_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TimeZone" field.</summary>
    public const int TimeZoneFieldNumber = 6;
    private string timeZone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TimeZone {
      get { return timeZone_; }
      set {
        timeZone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "ContactInfo" field.</summary>
    public const int ContactInfoFieldNumber = 7;
    private string contactInfo_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ContactInfo {
      get { return contactInfo_; }
      set {
        contactInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as LocationInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(LocationInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocationId != other.LocationId) return false;
      if (LocationName != other.LocationName) return false;
      if (Address != other.Address) return false;
      if (Latitude != other.Latitude) return false;
      if (Longitude != other.Longitude) return false;
      if (TimeZone != other.TimeZone) return false;
      if (ContactInfo != other.ContactInfo) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocationId.Length != 0) hash ^= LocationId.GetHashCode();
      if (LocationName.Length != 0) hash ^= LocationName.GetHashCode();
      if (Address.Length != 0) hash ^= Address.GetHashCode();
      if (Latitude.Length != 0) hash ^= Latitude.GetHashCode();
      if (Longitude.Length != 0) hash ^= Longitude.GetHashCode();
      if (TimeZone.Length != 0) hash ^= TimeZone.GetHashCode();
      if (ContactInfo.Length != 0) hash ^= ContactInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocationId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LocationId);
      }
      if (LocationName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(LocationName);
      }
      if (Address.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Address);
      }
      if (Latitude.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Latitude);
      }
      if (Longitude.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Longitude);
      }
      if (TimeZone.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(ContactInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocationId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationId);
      }
      if (LocationName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LocationName);
      }
      if (Address.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Address);
      }
      if (Latitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Latitude);
      }
      if (Longitude.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Longitude);
      }
      if (TimeZone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TimeZone);
      }
      if (ContactInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ContactInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(LocationInfo other) {
      if (other == null) {
        return;
      }
      if (other.LocationId.Length != 0) {
        LocationId = other.LocationId;
      }
      if (other.LocationName.Length != 0) {
        LocationName = other.LocationName;
      }
      if (other.Address.Length != 0) {
        Address = other.Address;
      }
      if (other.Latitude.Length != 0) {
        Latitude = other.Latitude;
      }
      if (other.Longitude.Length != 0) {
        Longitude = other.Longitude;
      }
      if (other.TimeZone.Length != 0) {
        TimeZone = other.TimeZone;
      }
      if (other.ContactInfo.Length != 0) {
        ContactInfo = other.ContactInfo;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LocationId = input.ReadString();
            break;
          }
          case 18: {
            LocationName = input.ReadString();
            break;
          }
          case 26: {
            Address = input.ReadString();
            break;
          }
          case 34: {
            Latitude = input.ReadString();
            break;
          }
          case 42: {
            Longitude = input.ReadString();
            break;
          }
          case 50: {
            TimeZone = input.ReadString();
            break;
          }
          case 58: {
            ContactInfo = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
