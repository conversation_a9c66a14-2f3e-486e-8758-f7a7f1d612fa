﻿using Microsoft.AspNetCore.Http;
using VisionIQ.Interfaces.Persistence;

namespace VisionIQ.Persistence.Contexts
{
    public class LoggedInUserService : ILoggedInUserService
    {
        public LoggedInUserService(IHttpContextAccessor httpContextAccessor)
        {
            UserId = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "uid")?.Value;

            LoginName = httpContextAccessor.HttpContext?.User.Identity?.Name ?? "Anonymous";

           
        }

        public string? UserId { get; }
        public string CompanyId { get; }
        public string CompanyName { get; }
        public bool IsParent { get; }
        public string Role { get; }
        public string RequestedUrl { get; }
        public List<string> Permissions { get; }
        public string LoginName { get; }
        public bool IsAuthenticated { get; }
        public bool IsAdministrator { get; }
        public bool IsSiteAdmin { get; }
        public string IpAddress { get; }
        
    }
}
