{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"VisionIQ.Persistence/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.7", "Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Pomelo.EntityFrameworkCore.MySql": "9.0.0-rc.1.efcore.9.0.0", "VisionIQ.CrossCuttingConcerns": "1.0.0", "VisionIQ.Interfaces": "1.0.0"}, "runtime": {"VisionIQ.Persistence.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "9.0.7", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "9.0.7", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "Microsoft.AspNetCore.Http/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "2.3.0", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Net.Http.Headers": "2.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.25014"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.25014"}}}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.25014"}}}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.25014"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.6": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/9.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.7", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31607"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.7": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "System.Formats.Asn1": "9.0.7", "System.Text.Json": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31607"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.ObjectPool/8.0.11": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "9.0.7"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.Net.Http.Headers/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7", "System.Buffers": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.25014"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MySqlConnector/2.4.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.4.0.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "9.0.4.0", "fileVersion": "9.0.4.0"}}}, "Pomelo.EntityFrameworkCore.MySql/9.0.0-rc.1.efcore.9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.7", "MySqlConnector": "2.4.0"}, "runtime": {"lib/net8.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "System.Buffers/4.6.0": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.7"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/9.0.7": {}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "9.0.7"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "9.0.7"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/9.0.7": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "VisionIQ.CrossCuttingConcerns/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "VisionIQ.Domain": "1.0.0"}, "runtime": {"VisionIQ.CrossCuttingConcerns.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "VisionIQ.Domain/1.0.0": {"runtime": {"VisionIQ.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "VisionIQ.Interfaces/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "VisionIQ.Domain": "1.0.0"}, "runtime": {"VisionIQ.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"VisionIQ.Persistence/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I9azEG2tZ4DDHAFgv+N38e6Yhttvf+QjE2j2UYyCACE7Swm5/0uoihCMWZ87oOZYeqiEFSxbsfpT71OYHe2tpw==", "path": "microsoft.aspnetcore.http/2.3.0", "hashPath": "microsoft.aspnetcore.http.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-39r9PPrjA6s0blyFv5qarckjNkaHRA5B+3b53ybuGGNTXEj1/DStQJ4NWjFL6QTRQpL9zt7nDyKxZdJOlcnq+Q==", "path": "microsoft.aspnetcore.http.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-f10WUgcsKqrkmnz6gt8HeZ7kyKjYN30PO7cSic1lPtH7paPtnQqXPOveul/SIPI43PhRD4trttg4ywnrEmmJpA==", "path": "microsoft.aspnetcore.http.features/2.3.0", "hashPath": "microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-trbXdWzoAEUVd0PE2yTopkz4kjZaAIA7xUWekd5uBw+7xE8Do/YOVTeb9d9koPTlbtZT539aESJjSLSqD8eYrQ==", "path": "microsoft.aspnetcore.webutilities/2.3.0", "hashPath": "microsoft.aspnetcore.webutilities.2.3.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-+pz7gIPh5ydsBcQvivt4R98PwJXer86fyQBBToIBLxZ5kuhW4N13Ijz87s9WpuPtF1vh4JesYCgpDPAOgkMhdg==", "path": "microsoft.data.sqlclient/5.1.6", "hashPath": "microsoft.data.sqlclient.5.1.6.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-PbD0q5ax15r91jD4TN7xbDCjldZSz4JfpYN4ZZjAkWeUyROkV92Ydg0O2/1keFA+2u3KPsDkJMmBKv2zQ06ZVg==", "path": "microsoft.entityframeworkcore/9.0.7", "hashPath": "microsoft.entityframeworkcore.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-YUXNerEkCf4OANO+zjuMznpUW7R8XxSCqmBfYhBrbrJVc09i84KkNgeUTaOUXCGogSK/3d7ORRhMqfUobnejBg==", "path": "microsoft.entityframeworkcore.abstractions/9.0.7", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-HqiPjAvjVOsyA1svnjL81/Wk2MRQYMK/lxKVWvw0f5IcA//VcxBepVSAqe7CFirdsPXqe8rFKEwZROWZTz7Jqw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.7", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-Yo5joquG7L79H5BhtpqP8apu+KFOAYfvmj0dZnVkPElBY14wY5qva0SOcrDWzYw5BrJrhIArfCcJCJHBvMYiKg==", "path": "microsoft.entityframeworkcore.relational/9.0.7", "hashPath": "microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-xNWKpPWY97Cm/+htOdlLprzaIVsZpjiBH7Bz94WqwwlpHzDMULQhT57jsvOxfgK4vphp2qWoJcmyU1mfmvlqTA==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.7", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-30necCQehcg9lFkMEIE7HczcoYGML8GUH6jlincA18d896fLZM9wl5tpTPJHgzANQE/6KXRLZSWbgevgg5csSw==", "path": "microsoft.extensions.caching.abstractions/9.0.7", "hashPath": "microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nDu6c8fwrHQYccLnWnvyElrdkL3rZ97TZNqL+niMFUcApVBHdpDmKcRvciGymJ4Y0iLDTOo5J2XhDQEbNb+dFg==", "path": "microsoft.extensions.caching.memory/9.0.7", "hashPath": "microsoft.extensions.caching.memory.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "path": "microsoft.extensions.dependencyinjection/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "path": "microsoft.extensions.logging/9.0.7", "hashPath": "microsoft.extensions.logging.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-6ApKcHNJigXBfZa6XlDQ8feJpq7SG1ogZXg6M4FiNzgd6irs3LUAzo0Pfn4F2ZI9liGnH1XIBR/OtSbZmJAV5w==", "path": "microsoft.extensions.objectpool/8.0.11", "hashPath": "microsoft.extensions.objectpool.8.0.11.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/M0wVg6tJUOHutWD3BMOUVZAioJVXe0tCpFiovzv0T9T12TBf4MnaHP0efO8TCr1a6O9RZgQeZ9Gdark8L9XdA==", "path": "microsoft.net.http.headers/2.3.0", "hashPath": "microsoft.net.http.headers.2.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MySqlConnector/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-78M+gVOjbdZEDIyXQqcA7EYlCGS3tpbUELHvn6638A2w0pkPI625ixnzsa5staAd3N9/xFmPJtkKDYwsXpFi/w==", "path": "mysqlconnector/2.4.0", "hashPath": "mysqlconnector.2.4.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/9.0.0-rc.1.efcore.9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jJ3aVp2FWamRA4Db6IJnt6MsC5A45vfLWDpy8VIiEV/CQiDFDgQb7Fk8O7Zyb8BVDhU6TfEXHujVBsFYxbwQbQ==", "path": "pomelo.entityframeworkcore.mysql/9.0.0-rc.1.efcore.9.0.0", "hashPath": "pomelo.entityframeworkcore.mysql.9.0.0-rc.1.efcore.9.0.0.nupkg.sha512"}, "System.Buffers/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "path": "system.buffers/4.6.0", "hashPath": "system.buffers.4.6.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-aXUqSscxVoR2qb4H+ybceGO2vfuUNqBiDX8rp/KdafjcTveRD60/JXkzV6PSqw/6jMFrfE6BJmH6P1UOegTfsw==", "path": "system.formats.asn1/9.0.7", "hashPath": "system.formats.asn1.9.0.7.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "path": "system.text.json/9.0.7", "hashPath": "system.text.json.9.0.7.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "VisionIQ.CrossCuttingConcerns/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "VisionIQ.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "VisionIQ.Interfaces/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}