{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"VisionIQ.CrossCuttingConcerns/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "VisionIQ.Domain": "1.0.0"}, "runtime": {"VisionIQ.CrossCuttingConcerns.dll": {}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "VisionIQ.Domain/1.0.0": {"runtime": {"VisionIQ.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"VisionIQ.CrossCuttingConcerns/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "VisionIQ.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}