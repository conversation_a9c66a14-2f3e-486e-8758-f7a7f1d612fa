﻿namespace VisionIQ.CrossCuttingConcerns.Extensions;

public static partial class Extensions
{
    public static IEnumerable<T> ForEach<T>(this IEnumerable<T> @this, Action<T> action)
    {
        T[] array = @this.ToArray();
        foreach (T t in array)
        {
            action(t);
        }
        return array;
    }
    public static IEnumerable<T> ForEach<T>(this IEnumerable<T> @this, Action<T, int> action)
    {
        T[] array = @this.ToArray();

        for (int i = 0; i < array.Length; i++)
        {
            action(array[i], i);
        }

        return array;
    }

    public static (List<T> Matching, List<T> NonMatching) Partition<T>(
        this IEnumerable<T> source, Func<T, bool> predicate)
    {
        var matching = new List<T>();
        var nonMatching = new List<T>();

        foreach (var item in source)
        {
            (predicate(item) ? matching : nonMatching).Add(item);
        }

        return (matching, nonMatching);
    }
}