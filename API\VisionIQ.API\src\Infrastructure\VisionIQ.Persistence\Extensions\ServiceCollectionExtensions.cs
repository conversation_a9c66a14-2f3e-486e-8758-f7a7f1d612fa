using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using VisionIQ.Interfaces.Persistence;
using VisionIQ.Interfaces.Repositories;
using VisionIQ.Persistence.Contexts;
using VisionIQ.Persistence.Repositories;

namespace VisionIQ.Persistence.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration config)
    {
        services.AddDatabaseContext<ApplicationDbContext>(config)
            .AddSingleton<ILoggerFactory, LoggerFactory>()
            .AddScoped(typeof(IRepository<>), typeof(BaseRepository<>))
            .AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>())
            .AddScoped<DbContext, ApplicationDbContext>()
            .AddScoped<ILocationRepository, LocationRepository>()
            .AddScoped<ITagRepository,TagRepository>();


        return services;
    }


    public static IServiceCollection AddDatabaseContext<T>(this IServiceCollection services, IConfiguration config)
     where T : DbContext
    {
        var provider = config.GetConnectionString("DBProvider");
        var connectionString = config.GetConnectionString("Default");
        var commandTimeout = 180;

        try
        {
            switch (provider.ToLower())
            {
                case "mysql":

                    services.AddDbContext<T>(options =>
                        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), sqlOptions =>
                        {
                            // Enable retry on transient failures (e.g., network issues)
                            sqlOptions.EnableRetryOnFailure(
                                maxRetryCount: 5,
                                maxRetryDelay: TimeSpan.FromSeconds(30),
                                errorNumbersToAdd: null);

                            // Set command timeout (in seconds)
                            sqlOptions.CommandTimeout(commandTimeout);
                        }));
                    break;

                case "mssql":

                    services.AddDbContext<T>(options => options.UseSqlServer(connectionString, sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure(
                            5,
                            TimeSpan.FromSeconds(30),
                            null);

                        sqlOptions.CommandTimeout(commandTimeout);
                    }));
                    break;

                case "postgres":
                    services.AddDbContext<T>(options =>
                        options.UseNpgsql(connectionString, npgsqlOptionsAction: npgsqlOptions =>
                        {
                            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
                            npgsqlOptions.EnableRetryOnFailure(
                                maxRetryCount: 3,
                                maxRetryDelay: TimeSpan.FromSeconds(10),
                                errorCodesToAdd: null);

                            npgsqlOptions.CommandTimeout(commandTimeout);

                        }));

                    break;

                default:
                    services.AddDbContext<T>(options =>
                        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), sqlOptions =>
                        {
                            // Enable retry on transient failures (e.g., network issues)
                            sqlOptions.EnableRetryOnFailure(
                                maxRetryCount: 5,
                                maxRetryDelay: TimeSpan.FromSeconds(30),
                                errorNumbersToAdd: null);

                            // Set command timeout (in seconds)
                            sqlOptions.CommandTimeout(commandTimeout);
                        }));
                    break;
            }
        }
        catch (Exception ex)
        {
            //Log.Error($"Error in AddDatabaseContext : {ex.GetMessage()}");
        }
        return services;
    }
}
