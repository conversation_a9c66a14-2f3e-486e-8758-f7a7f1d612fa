﻿namespace VisionIQ.CrossCuttingConcerns.Extensions;

public static partial class Extensions
{
    public static ulong ToUInt64(this object @this)
    {
        return Convert.ToUInt64(@this);
    }
    public static long ToInt64(this object @this)
    {
        return Convert.ToInt64(@this);
    }
    public static bool ToBoolean(this object @this)
    {
        return Convert.ToBoolean(@this);
    }
    public static DateTime ToDateTime(this object @this)
    {
        return Convert.ToDateTime(@this);
    }
}